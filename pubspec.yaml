name: fintrack
description: A new Flutter project.
publish_to: none
version: 1.0.1+3

environment:
  sdk: ^3.6.0

dependencies:
  flutter:       # 🚨 CRITICAL: Required for every Flutter project - DO NOT REMOVE
    sdk: flutter # 🚨 CRITICAL: Required for every Flutter project - DO NOT REMOVE
  
  # 🚨 CRITICAL: Core UI and responsive design - DO NOT REMOVE OR MODIFY
  sizer: ^2.0.15              # Required for responsive design system
  flutter_svg: ^2.0.9        # Required for SVG icon support
  google_fonts: ^6.1.0       # Required for typography (replaces local fonts)
  shared_preferences: ^2.2.2 # Required for local data storage
  web: ^1.1.1
  
  # Feature dependencies - safe to modify
  cached_network_image: ^3.3.1
  connectivity_plus: ^6.1.4
  dio: ^5.4.0
  fluttertoast: ^8.2.4
  fl_chart: ^0.65.0
  intl: ^0.19.0
  path_provider: ^2.1.2
  permission_handler: ^11.3.1
  pdf: ^3.10.7
  excel: ^4.0.3
  open_file: ^3.3.2
  sqflite: ^2.3.0
  url_launcher: ^6.2.4
  google_mobile_ads: ^5.1.0

dev_dependencies:
  flutter_test:    # 🚨 CRITICAL: Required for Flutter project testing - DO NOT REMOVE
    sdk: flutter   # 🚨 CRITICAL: Required for Flutter project testing - DO NOT REMOVE
  flutter_lints: ^5.0.0 # 🚨 CRITICAL: Required for code quality - DO NOT REMOVE

flutter:
  uses-material-design: true # 🚨 CRITICAL: Required for Material icon font - DO NOT REMOVE
  assets:
    - assets/
    - assets/images/
  # 🚨 CRITICAL ASSET MANAGEMENT RULES:
  # - DO NOT ADD NEW ASSET DIRECTORIES (assets/svg/, assets/icons/, etc.)
  # - ONLY USE EXISTING AND ITEMS AVAILABLE IN THE DIRECTORIES LISTED ABOVE (assets/, assets/images/)
  
  # 🚨 CRITICAL FONTS RULE: 
  # - THIS PROJECT USES GOOGLE FONTS INSTEAD OF LOCAL FONTS
  # - DO NOT ADD ANY LOCAL FONTS SECTION OR FONT FILES
