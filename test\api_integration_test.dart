import 'package:flutter_test/flutter_test.dart';
import 'package:fintrack/core/config/api_config.dart';
import 'package:fintrack/core/utils/api_test_helper.dart';

void main() {
  group('API Integration Tests', () {
    setUpAll(() async {
      // Initialize API configuration before running tests
      await ApiConfig.initialize();
    });

    test('API Connection Test', () async {
      final result = await ApiTestHelper.testConnection();
      expect(result, isTrue, reason: 'API connection should be successful');
    });

    test('Authentication Flow Test', () async {
      final result = await ApiTestHelper.testAuthFlow(
        name: 'Flutter Test User',
        email: 'flutter.test.${DateTime.now().millisecondsSinceEpoch}@example.com',
        password: 'flutter123',
      );
      expect(result, isTrue, reason: 'Complete authentication flow should work');
    });

    test('Check Authentication Status', () async {
      await ApiTestHelper.checkAuthStatus();
      // This test just checks status, no assertion needed
    });
  });
}
