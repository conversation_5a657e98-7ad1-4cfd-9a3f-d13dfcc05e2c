import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class FilterBottomSheetWidget extends StatefulWidget {
  final Map<String, dynamic> currentFilters;
  final Function(Map<String, dynamic>) onFiltersChanged;

  const FilterBottomSheetWidget({
    super.key,
    required this.currentFilters,
    required this.onFiltersChanged,
  });

  @override
  State<FilterBottomSheetWidget> createState() =>
      _FilterBottomSheetWidgetState();
}

class _FilterBottomSheetWidgetState extends State<FilterBottomSheetWidget> {
  late Map<String, dynamic> _filters;
  late RangeValues _amountRange;

  final List<String> _categories = [
    'Food & Dining',
    'Transportation',
    'Shopping',
    'Entertainment',
    'Bills & Utilities',
    'Healthcare',
    'Education',
    'Travel',
    'Groceries',
    'Fuel',
    'Investment',
    'Salary',
    'Business',
    'Other'
  ];

  final List<String> _accountTypes = ['Cash', 'UPI', 'Bank', 'Wallet'];

  @override
  void initState() {
    super.initState();
    _filters = Map<String, dynamic>.from(widget.currentFilters);
    _amountRange = RangeValues(
      (_filters['minAmount'] as double?) ?? 0.0,
      (_filters['maxAmount'] as double?) ?? 100000.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 2.h),
            width: 12.w,
            height: 0.5.h,
            decoration: BoxDecoration(
              color: isDark
                  ? AppTheme.neutralGray.withValues(alpha: 0.5)
                  : AppTheme.borderSubtle,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Transactions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: _clearAllFilters,
                      child: Text(
                        'Clear All',
                        style: TextStyle(color: AppTheme.alertRed),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        widget.onFiltersChanged(_filters);
                        Navigator.pop(context);
                      },
                      child: Text(
                        'Apply',
                        style: TextStyle(color: AppTheme.primaryTeal),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date Range Section
                  _buildSectionHeader('Date Range'),
                  _buildDateRangeSection(),

                  SizedBox(height: 3.h),

                  // Categories Section
                  _buildSectionHeader('Categories'),
                  _buildCategoriesSection(),

                  SizedBox(height: 3.h),

                  // Account Types Section
                  _buildSectionHeader('Account Types'),
                  _buildAccountTypesSection(),

                  SizedBox(height: 3.h),

                  // Amount Range Section
                  _buildSectionHeader('Amount Range'),
                  _buildAmountRangeSection(),

                  SizedBox(height: 3.h),

                  // Transaction Type Section
                  _buildSectionHeader('Transaction Type'),
                  _buildTransactionTypeSection(),

                  SizedBox(height: 4.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 2.h),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
      ),
    );
  }

  Widget _buildDateRangeSection() {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark
              ? AppTheme.neutralGray.withValues(alpha: 0.2)
              : AppTheme.borderSubtle,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => _selectDate(context, true),
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      color: isDark
                          ? AppTheme.cardSurfaceDark
                          : AppTheme.cardSurface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isDark
                            ? AppTheme.neutralGray.withValues(alpha: 0.3)
                            : AppTheme.borderSubtle,
                      ),
                    ),
                    child: Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'calendar_today',
                          color: AppTheme.primaryTeal,
                          size: 4.w,
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          _filters['startDate'] != null
                              ? _formatDate(_filters['startDate'] as DateTime)
                              : 'Start Date',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: GestureDetector(
                  onTap: () => _selectDate(context, false),
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      color: isDark
                          ? AppTheme.cardSurfaceDark
                          : AppTheme.cardSurface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isDark
                            ? AppTheme.neutralGray.withValues(alpha: 0.3)
                            : AppTheme.borderSubtle,
                      ),
                    ),
                    child: Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'calendar_today',
                          color: AppTheme.primaryTeal,
                          size: 4.w,
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          _filters['endDate'] != null
                              ? _formatDate(_filters['endDate'] as DateTime)
                              : 'End Date',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection() {
    final selectedCategories = (_filters['categories'] as List<String>?) ?? [];

    return Wrap(
      spacing: 2.w,
      runSpacing: 1.h,
      children: _categories.map((category) {
        final isSelected = selectedCategories.contains(category);
        return GestureDetector(
          onTap: () => _toggleCategory(category),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: isSelected ? AppTheme.primaryTeal : Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? AppTheme.primaryTeal : AppTheme.neutralGray,
              ),
            ),
            child: Text(
              category,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isSelected ? Colors.white : null,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                  ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAccountTypesSection() {
    final selectedAccounts = (_filters['accountTypes'] as List<String>?) ?? [];

    return Wrap(
      spacing: 2.w,
      runSpacing: 1.h,
      children: _accountTypes.map((account) {
        final isSelected = selectedAccounts.contains(account);
        return GestureDetector(
          onTap: () => _toggleAccountType(account),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
            decoration: BoxDecoration(
              color: isSelected ? AppTheme.primaryTeal : Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? AppTheme.primaryTeal : AppTheme.neutralGray,
              ),
            ),
            child: Text(
              account,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isSelected ? Colors.white : null,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                  ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAmountRangeSection() {
    return Column(
      children: [
        RangeSlider(
          values: _amountRange,
          min: 0,
          max: 100000,
          divisions: 100,
          labels: RangeLabels(
            '₹${_amountRange.start.round()}',
            '₹${_amountRange.end.round()}',
          ),
          onChanged: (RangeValues values) {
            setState(() {
              _amountRange = values;
              _filters['minAmount'] = values.start;
              _filters['maxAmount'] = values.end;
            });
          },
        ),
        SizedBox(height: 2.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Min: ₹${_amountRange.start.round()}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'Max: ₹${_amountRange.end.round()}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTransactionTypeSection() {
    final selectedType = _filters['transactionType'] as String?;
    final types = ['All', 'Income', 'Expense'];

    return Row(
      children: types.map((type) {
        final isSelected =
            selectedType == type || (selectedType == null && type == 'All');
        return Expanded(
          child: GestureDetector(
            onTap: () => _selectTransactionType(type),
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 1.w),
              padding: EdgeInsets.symmetric(vertical: 2.h),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.primaryTeal : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      isSelected ? AppTheme.primaryTeal : AppTheme.neutralGray,
                ),
              ),
              child: Center(
                child: Text(
                  type,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isSelected ? Colors.white : null,
                        fontWeight:
                            isSelected ? FontWeight.w500 : FontWeight.w400,
                      ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  void _toggleCategory(String category) {
    setState(() {
      final categories =
          (_filters['categories'] as List<String>?) ?? <String>[];
      if (categories.contains(category)) {
        categories.remove(category);
      } else {
        categories.add(category);
      }
      _filters['categories'] = categories;
    });
  }

  void _toggleAccountType(String accountType) {
    setState(() {
      final accountTypes =
          (_filters['accountTypes'] as List<String>?) ?? <String>[];
      if (accountTypes.contains(accountType)) {
        accountTypes.remove(accountType);
      } else {
        accountTypes.add(accountType);
      }
      _filters['accountTypes'] = accountTypes;
    });
  }

  void _selectTransactionType(String type) {
    setState(() {
      _filters['transactionType'] = type == 'All' ? null : type;
    });
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _filters['startDate'] = picked;
        } else {
          _filters['endDate'] = picked;
        }
      });
    }
  }

  void _clearAllFilters() {
    setState(() {
      _filters.clear();
      _amountRange = const RangeValues(0.0, 100000.0);
    });
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}';
  }
}
