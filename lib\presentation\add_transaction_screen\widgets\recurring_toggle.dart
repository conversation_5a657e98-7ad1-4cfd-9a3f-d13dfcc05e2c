import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class RecurringToggle extends StatelessWidget {
  final bool isRecurring;
  final String? selectedFrequency;
  final ValueChanged<bool> onToggle;
  final ValueChanged<String> onFrequencyChanged;

  const RecurringToggle({
    Key? key,
    required this.isRecurring,
    this.selectedFrequency,
    required this.onToggle,
    required this.onFrequencyChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Recurring Transaction',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Switch(
                value: isRecurring,
                onChanged: onToggle,
                activeColor: AppTheme.primaryTeal,
                activeTrackColor: AppTheme.primaryTeal.withValues(alpha: 0.3),
                inactiveThumbColor: AppTheme.neutralGray,
                inactiveTrackColor: AppTheme.neutralGray.withValues(alpha: 0.2),
              ),
            ],
          ),
          if (isRecurring) ...[
            SizedBox(height: 2.h),
            GestureDetector(
              onTap: () => _showFrequencySelector(context),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: AppTheme.lightTheme.colorScheme.surface,
                  border: Border.all(color: AppTheme.borderSubtle),
                ),
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'repeat',
                      color: AppTheme.primaryTeal,
                      size: 20,
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Text(
                        selectedFrequency ?? 'Select Frequency',
                        style:
                            AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: selectedFrequency != null
                              ? AppTheme.textHighEmphasisLight
                              : AppTheme.textDisabledLight,
                        ),
                      ),
                    ),
                    CustomIconWidget(
                      iconName: 'keyboard_arrow_down',
                      color: AppTheme.textMediumEmphasisLight,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showFrequencySelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 1.h),
              width: 12.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: AppTheme.borderSubtle,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Text(
                'Select Frequency',
                style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              itemCount: _frequencies.length,
              itemBuilder: (context, index) {
                final frequency = _frequencies[index];
                final isSelected = selectedFrequency == frequency['name'];

                return ListTile(
                  leading: Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryTeal.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: CustomIconWidget(
                      iconName: frequency['icon'] as String,
                      color: AppTheme.primaryTeal,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    frequency['name'] as String,
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppTheme.primaryTeal : null,
                    ),
                  ),
                  subtitle: Text(
                    frequency['description'] as String,
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.textMediumEmphasisLight,
                    ),
                  ),
                  trailing: isSelected
                      ? CustomIconWidget(
                          iconName: 'check_circle',
                          color: AppTheme.primaryTeal,
                          size: 24,
                        )
                      : null,
                  onTap: () {
                    onFrequencyChanged(frequency['name'] as String);
                    Navigator.pop(context);
                  },
                );
              },
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  static final List<Map<String, dynamic>> _frequencies = [
    {"name": "Daily", "icon": "today", "description": "Repeats every day"},
    {
      "name": "Weekly",
      "icon": "date_range",
      "description": "Repeats every week"
    },
    {
      "name": "Monthly",
      "icon": "calendar_month",
      "description": "Repeats every month"
    },
    {"name": "Yearly", "icon": "event", "description": "Repeats every year"},
  ];
}
