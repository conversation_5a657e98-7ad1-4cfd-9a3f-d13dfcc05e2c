import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class AmountInputField extends StatelessWidget {
  final TextEditingController controller;
  final bool isIncome;
  final String? errorText;

  const AmountInputField({
    Key? key,
    required this.controller,
    required this.isIncome,
    this.errorText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: FormattingService.instance,
      builder: (context, child) {
        return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Amount',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 1.h),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: AppTheme.lightTheme.colorScheme.surface,
              border: Border.all(
                color: errorText != null
                    ? AppTheme.alertRed
                    : AppTheme.borderSubtle,
                width: errorText != null ? 2 : 1,
              ),
            ),
            child: TextFormField(
              controller: controller,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              style: AppTheme.financialAmountStyle(
                isLight: true,
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
              ).copyWith(
                color: isIncome ? AppTheme.successGreen : AppTheme.alertRed,
              ),
              decoration: InputDecoration(
                prefixIcon: Container(
                  padding: EdgeInsets.all(3.w),
                  child: Text(
                    FormattingService.instance.currencySymbol,
                    style: AppTheme.financialAmountStyle(
                      isLight: true,
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w600,
                    ).copyWith(
                      color:
                          isIncome ? AppTheme.successGreen : AppTheme.alertRed,
                    ),
                  ),
                ),
                hintText: '0.00',
                hintStyle:
                    AppTheme.lightTheme.textTheme.headlineMedium?.copyWith(
                  color: AppTheme.textDisabledLight,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 4.w,
                  vertical: 2.h,
                ),
              ),
            ),
          ),
          if (errorText != null) ...[
            SizedBox(height: 0.5.h),
            Padding(
              padding: EdgeInsets.only(left: 2.w),
              child: Text(
                errorText!,
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.alertRed,
                ),
              ),
            ),
          ],
        ],
      ),
        );
      },
    );
  }
}
