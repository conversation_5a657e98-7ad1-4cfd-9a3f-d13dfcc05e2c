import 'package:flutter/foundation.dart';
import '../services/auth_service.dart';
import '../models/api_response.dart';

/// Helper class for testing API integration
class ApiTestHelper {
  static final AuthService _authService = AuthService.instance;

  /// Test API connection
  static Future<bool> testConnection() async {
    try {
      debugPrint('🧪 Testing API connection...');
      final response = await _authService.testConnection();
      
      if (response.success) {
        debugPrint('✅ API connection successful: ${response.message}');
        return true;
      } else {
        debugPrint('❌ API connection failed: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ API connection error: $e');
      return false;
    }
  }

  /// Test user registration
  static Future<bool> testRegistration({
    String name = 'Test User',
    String email = '<EMAIL>',
    String password = 'test123',
  }) async {
    try {
      debugPrint('🧪 Testing user registration...');
      final response = await _authService.register(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: password,
      );
      
      if (response.success) {
        debugPrint('✅ Registration successful: ${response.message}');
        debugPrint('👤 User: ${response.data?.user.name} (${response.data?.user.email})');
        debugPrint('🔑 Token: ${response.data?.token.substring(0, 20)}...');
        return true;
      } else {
        debugPrint('❌ Registration failed: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Registration error: $e');
      return false;
    }
  }

  /// Test user login
  static Future<bool> testLogin({
    String email = '<EMAIL>',
    String password = 'test123',
  }) async {
    try {
      debugPrint('🧪 Testing user login...');
      final response = await _authService.login(
        email: email,
        password: password,
      );
      
      if (response.success) {
        debugPrint('✅ Login successful: ${response.message}');
        debugPrint('👤 User: ${response.data?.user.name} (${response.data?.user.email})');
        debugPrint('🔑 Token: ${response.data?.token.substring(0, 20)}...');
        return true;
      } else {
        debugPrint('❌ Login failed: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Login error: $e');
      return false;
    }
  }

  /// Test get user profile
  static Future<bool> testGetProfile() async {
    try {
      debugPrint('🧪 Testing get user profile...');
      final response = await _authService.getProfile();
      
      if (response.success) {
        debugPrint('✅ Get profile successful: ${response.message}');
        debugPrint('👤 User: ${response.data?.name} (${response.data?.email})');
        return true;
      } else {
        debugPrint('❌ Get profile failed: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Get profile error: $e');
      return false;
    }
  }

  /// Test user logout
  static Future<bool> testLogout() async {
    try {
      debugPrint('🧪 Testing user logout...');
      final response = await _authService.logout();
      
      if (response.success) {
        debugPrint('✅ Logout successful: ${response.message}');
        return true;
      } else {
        debugPrint('❌ Logout failed: ${response.message}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Logout error: $e');
      return false;
    }
  }

  /// Run all API tests
  static Future<void> runAllTests() async {
    debugPrint('🚀 Starting API integration tests...');
    debugPrint('=' * 50);

    // Test 1: Connection
    final connectionTest = await testConnection();
    debugPrint('');

    if (!connectionTest) {
      debugPrint('❌ Connection test failed. Stopping tests.');
      return;
    }

    // Test 2: Registration (optional - might fail if user exists)
    await testRegistration();
    debugPrint('');

    // Test 3: Login
    final loginTest = await testLogin();
    debugPrint('');

    if (!loginTest) {
      debugPrint('❌ Login test failed. Stopping tests.');
      return;
    }

    // Test 4: Get Profile
    await testGetProfile();
    debugPrint('');

    // Test 5: Logout
    await testLogout();
    debugPrint('');

    debugPrint('=' * 50);
    debugPrint('🎉 API integration tests completed!');
  }

  /// Test authentication flow
  static Future<bool> testAuthFlow({
    String name = 'Flutter Test User',
    String email = '<EMAIL>',
    String password = 'flutter123',
  }) async {
    debugPrint('🧪 Testing complete authentication flow...');
    
    // Step 1: Test connection
    if (!await testConnection()) {
      return false;
    }

    // Step 2: Try registration (might fail if user exists)
    await testRegistration(name: name, email: email, password: password);

    // Step 3: Login
    if (!await testLogin(email: email, password: password)) {
      return false;
    }

    // Step 4: Get profile
    if (!await testGetProfile()) {
      return false;
    }

    // Step 5: Logout
    if (!await testLogout()) {
      return false;
    }

    debugPrint('✅ Complete authentication flow test passed!');
    return true;
  }

  /// Check authentication status
  static Future<void> checkAuthStatus() async {
    final isAuthenticated = await _authService.isAuthenticated();
    final currentUser = await _authService.getCurrentUser();
    final hasValidToken = await _authService.hasValidToken();

    debugPrint('🔍 Authentication Status:');
    debugPrint('  - Is Authenticated: $isAuthenticated');
    debugPrint('  - Has Valid Token: $hasValidToken');
    debugPrint('  - Current User: ${currentUser?.name ?? 'None'} (${currentUser?.email ?? 'None'})');
  }
}
