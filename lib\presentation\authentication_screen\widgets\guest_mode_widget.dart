import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class GuestModeWidget extends StatelessWidget {
  final VoidCallback onGuestLogin;
  final bool isLoading;

  const GuestModeWidget({
    Key? key,
    required this.onGuestLogin,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Divider with "Or" text
        Row(
          children: [
            Expanded(
              child: Container(
                height: 1,
                color: Theme.of(context).dividerColor,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Text(
                'Or',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.neutralGray,
                    ),
              ),
            ),
            Expanded(
              child: Container(
                height: 1,
                color: Theme.of(context).dividerColor,
              ),
            ),
          ],
        ),

        SizedBox(height: 3.h),

        // Guest Mode But<PERSON>(
          width: double.infinity,
          height: 6.h,
          child: OutlinedButton(
            onPressed: isLoading ? null : onGuestLogin,
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: AppTheme.primaryTeal,
                width: 1.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomIconWidget(
                  iconName: 'person_outline',
                  color: AppTheme.primaryTeal,
                  size: 5.w,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Continue as Guest',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppTheme.primaryTeal,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 2.h),

        // Guest Mode Info
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: AppTheme.primaryTeal.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.primaryTeal.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              CustomIconWidget(
                iconName: 'info_outline',
                color: AppTheme.primaryTeal,
                size: 4.w,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  'Guest mode stores data locally on your device only',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.primaryTeal,
                      ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
