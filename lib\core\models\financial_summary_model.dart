import 'dart:convert';
import 'transaction_model.dart';
import 'account_model.dart';
import '../services/formatting_service.dart';

/// Financial summary model for user's financial overview
class FinancialSummary {
  final double totalBalance;
  final double totalIncome;
  final double totalExpense;
  final double monthlyIncome;
  final double monthlyExpense;
  final double weeklyIncome;
  final double weeklyExpense;
  final double dailyIncome;
  final double dailyExpense;
  final List<Account> accounts;
  final List<Transaction> recentTransactions;
  final Map<String, double> categoryExpenses;
  final Map<String, double> categoryIncomes;
  final Map<String, double> monthlyTrends;
  final DateTime lastUpdated;

  const FinancialSummary({
    required this.totalBalance,
    required this.totalIncome,
    required this.totalExpense,
    required this.monthlyIncome,
    required this.monthlyExpense,
    required this.weeklyIncome,
    required this.weeklyExpense,
    required this.dailyIncome,
    required this.dailyExpense,
    required this.accounts,
    required this.recentTransactions,
    required this.categoryExpenses,
    required this.categoryIncomes,
    required this.monthlyTrends,
    required this.lastUpdated,
  });

  /// Create FinancialSummary from JSON
  factory FinancialSummary.fromJson(Map<String, dynamic> json) {
    return FinancialSummary(
      totalBalance: (json['total_balance'] is String) 
          ? double.parse(json['total_balance']) 
          : (json['total_balance'] as num).toDouble(),
      totalIncome: (json['total_income'] is String) 
          ? double.parse(json['total_income']) 
          : (json['total_income'] as num).toDouble(),
      totalExpense: (json['total_expense'] is String) 
          ? double.parse(json['total_expense']) 
          : (json['total_expense'] as num).toDouble(),
      monthlyIncome: (json['monthly_income'] is String) 
          ? double.parse(json['monthly_income']) 
          : (json['monthly_income'] as num).toDouble(),
      monthlyExpense: (json['monthly_expense'] is String) 
          ? double.parse(json['monthly_expense']) 
          : (json['monthly_expense'] as num).toDouble(),
      weeklyIncome: (json['weekly_income'] is String) 
          ? double.parse(json['weekly_income']) 
          : (json['weekly_income'] as num).toDouble(),
      weeklyExpense: (json['weekly_expense'] is String) 
          ? double.parse(json['weekly_expense']) 
          : (json['weekly_expense'] as num).toDouble(),
      dailyIncome: (json['daily_income'] is String) 
          ? double.parse(json['daily_income']) 
          : (json['daily_income'] as num).toDouble(),
      dailyExpense: (json['daily_expense'] is String) 
          ? double.parse(json['daily_expense']) 
          : (json['daily_expense'] as num).toDouble(),
      accounts: (json['accounts'] as List<dynamic>?)
          ?.map((account) => Account.fromJson(account))
          .toList() ?? [],
      recentTransactions: (json['recent_transactions'] as List<dynamic>?)
          ?.map((transaction) => Transaction.fromJson(transaction))
          .toList() ?? [],
      categoryExpenses: Map<String, double>.from(
        (json['category_expenses'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value is String) ? double.parse(value) : (value as num).toDouble()),
        ) ?? {},
      ),
      categoryIncomes: Map<String, double>.from(
        (json['category_incomes'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value is String) ? double.parse(value) : (value as num).toDouble()),
        ) ?? {},
      ),
      monthlyTrends: Map<String, double>.from(
        (json['monthly_trends'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value is String) ? double.parse(value) : (value as num).toDouble()),
        ) ?? {},
      ),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }

  /// Convert FinancialSummary to JSON
  Map<String, dynamic> toJson() {
    return {
      'total_balance': totalBalance,
      'total_income': totalIncome,
      'total_expense': totalExpense,
      'monthly_income': monthlyIncome,
      'monthly_expense': monthlyExpense,
      'weekly_income': weeklyIncome,
      'weekly_expense': weeklyExpense,
      'daily_income': dailyIncome,
      'daily_expense': dailyExpense,
      'accounts': accounts.map((account) => account.toJson()).toList(),
      'recent_transactions': recentTransactions.map((transaction) => transaction.toJson()).toList(),
      'category_expenses': categoryExpenses,
      'category_incomes': categoryIncomes,
      'monthly_trends': monthlyTrends,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  /// Get net income (income - expense)
  double get netIncome => totalIncome - totalExpense;

  /// Get monthly net income
  double get monthlyNetIncome => monthlyIncome - monthlyExpense;

  /// Get weekly net income
  double get weeklyNetIncome => weeklyIncome - weeklyExpense;

  /// Get daily net income
  double get dailyNetIncome => dailyIncome - dailyExpense;

  /// Get formatted total balance
  String get formattedTotalBalance {
    return FormattingService.instance.formatCurrency(totalBalance);
  }

  /// Get formatted monthly income
  String get formattedMonthlyIncome {
    return FormattingService.instance.formatCurrency(monthlyIncome);
  }

  /// Get formatted monthly expense
  String get formattedMonthlyExpense {
    return FormattingService.instance.formatCurrency(monthlyExpense);
  }

  /// Get formatted net income
  String get formattedNetIncome {
    final net = netIncome;
    final prefix = net >= 0 ? '+' : '';
    final formattedAmount = FormattingService.instance.formatCurrency(net.abs());
    return '$prefix$formattedAmount';
  }

  /// Get savings rate (percentage of income saved)
  double get savingsRate {
    if (monthlyIncome == 0) return 0;
    return ((monthlyIncome - monthlyExpense) / monthlyIncome) * 100;
  }

  /// Get formatted savings rate
  String get formattedSavingsRate {
    return FormattingService.instance.formatPercentage(savingsRate);
  }

  /// Get top expense categories (sorted by amount)
  List<MapEntry<String, double>> get topExpenseCategories {
    final entries = categoryExpenses.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(5).toList();
  }

  /// Get top income categories (sorted by amount)
  List<MapEntry<String, double>> get topIncomeCategories {
    final entries = categoryIncomes.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(5).toList();
  }

  /// Check if user is overspending
  bool get isOverspending => monthlyExpense > monthlyIncome;

  /// Get expense ratio (expense/income)
  double get expenseRatio {
    if (monthlyIncome == 0) return 0;
    return monthlyExpense / monthlyIncome;
  }

  /// Get budget health status
  String get budgetHealthStatus {
    final ratio = expenseRatio;
    if (ratio <= 0.5) return 'Excellent';
    if (ratio <= 0.7) return 'Good';
    if (ratio <= 0.9) return 'Fair';
    if (ratio <= 1.0) return 'Caution';
    return 'Over Budget';
  }

  /// Create empty financial summary
  factory FinancialSummary.empty() {
    return FinancialSummary(
      totalBalance: 0.0,
      totalIncome: 0.0,
      totalExpense: 0.0,
      monthlyIncome: 0.0,
      monthlyExpense: 0.0,
      weeklyIncome: 0.0,
      weeklyExpense: 0.0,
      dailyIncome: 0.0,
      dailyExpense: 0.0,
      accounts: [],
      recentTransactions: [],
      categoryExpenses: {},
      categoryIncomes: {},
      monthlyTrends: {},
      lastUpdated: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'FinancialSummary(totalBalance: $totalBalance, monthlyIncome: $monthlyIncome, monthlyExpense: $monthlyExpense)';
  }
}
