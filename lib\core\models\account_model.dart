import 'dart:convert';
import 'package:intl/intl.dart';
import '../services/formatting_service.dart';

/// Account model representing a user's financial account
class Account {
  final int? id;
  final int userId;
  final String name;
  final String type; // 'cash', 'bank', 'upi', 'credit_card', 'digital_wallet'
  final double balance;
  final String currency;
  final String? bankName;
  final String? accountNumber;
  final String? description;
  final bool isActive;
  final String? icon;
  final String? color;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Account({
    this.id,
    required this.userId,
    required this.name,
    required this.type,
    required this.balance,
    this.currency = 'INR',
    this.bankName,
    this.accountNumber,
    this.description,
    this.isActive = true,
    this.icon,
    this.color,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Account from JSON
  factory Account.fromJson(Map<String, dynamic> json) {
    return Account(
      id: json['id'] is String ? int.parse(json['id']) : json['id'],
      userId: json['user_id'] is String ? int.parse(json['user_id']) : json['user_id'],
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      balance: (json['balance'] is String) 
          ? double.parse(json['balance']) 
          : (json['balance'] as num).toDouble(),
      currency: json['currency'] ?? 'INR',
      bankName: json['bank_name'],
      accountNumber: json['account_number'],
      description: json['description'],
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      icon: json['icon'],
      color: json['color'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  /// Convert Account to JSON
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'user_id': userId,
      'name': name,
      'type': type,
      'balance': balance,
      'currency': currency,
      if (bankName != null) 'bank_name': bankName,
      if (accountNumber != null) 'account_number': accountNumber,
      if (description != null) 'description': description,
      'is_active': isActive ? 1 : 0,
      if (icon != null) 'icon': icon,
      if (color != null) 'color': color,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Convert Account to JSON string
  String toJsonString() {
    return json.encode(toJson());
  }

  /// Create Account from JSON string
  factory Account.fromJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return Account.fromJson(json);
  }

  /// Create a copy of Account with updated fields
  Account copyWith({
    int? id,
    int? userId,
    String? name,
    String? type,
    double? balance,
    String? currency,
    String? bankName,
    String? accountNumber,
    String? description,
    bool? isActive,
    String? icon,
    String? color,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Account(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      type: type ?? this.type,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get formatted balance with currency
  String get formattedBalance {
    return FormattingService.instance.formatCurrency(balance);
  }

  /// Get account icon name based on type
  String get accountIcon {
    if (icon != null) return icon!;

    switch (type.toLowerCase()) {
      case 'cash':
        return 'payments';
      case 'checking':
      case 'bank':
      case 'bank_account':
        return 'account_balance';
      case 'savings':
        return 'savings';
      case 'credit':
      case 'credit_card':
        return 'credit_card';
      case 'investment':
        return 'trending_up';
      case 'loan':
        return 'account_balance_wallet';
      case 'upi':
        return 'qr_code_scanner';
      case 'digital_wallet':
        return 'account_balance_wallet';
      default:
        return 'account_balance_wallet';
    }
  }

  /// Get account type for internal use
  String get accountType => type;

  /// Get account color based on type
  String get accountColor {
    if (color != null) return color!;
    
    switch (type.toLowerCase()) {
      case 'cash':
        return '#4CAF50'; // Green
      case 'bank':
      case 'bank_account':
        return '#9C27B0'; // Purple
      case 'upi':
        return '#00BCD4'; // Teal
      case 'credit_card':
        return '#F44336'; // Red
      case 'digital_wallet':
        return '#FF9800'; // Orange
      default:
        return '#757575'; // Gray
    }
  }

  /// Get display name with bank name if available
  String get displayName {
    if (bankName != null && bankName!.isNotEmpty) {
      return '$name ($bankName)';
    }
    return name;
  }

  /// Get masked account number for display
  String? get maskedAccountNumber {
    if (accountNumber == null || accountNumber!.length < 4) return null;
    
    final length = accountNumber!.length;
    if (length <= 4) return accountNumber;
    
    return '****${accountNumber!.substring(length - 4)}';
  }



  /// Get account type display name
  String get accountTypeDisplay {
    switch (type.toLowerCase()) {
      case 'checking':
        return 'Checking Account';
      case 'savings':
        return 'Savings Account';
      case 'credit':
      case 'credit_card':
        return 'Credit Card';
      case 'cash':
        return 'Cash';
      case 'investment':
        return 'Investment Account';
      case 'loan':
        return 'Loan Account';
      case 'bank':
      case 'bank_account':
        return 'Bank Account';
      case 'upi':
        return 'UPI Account';
      case 'digital_wallet':
        return 'Digital Wallet';
      case 'other':
        return 'Other Account';
      default:
        return type.replaceAll('_', ' ').split(' ')
            .map((word) => word.isNotEmpty
                ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                : '')
            .join(' ');
    }
  }

  /// Check if account has sufficient balance for transaction
  bool hasSufficientBalance(double amount) {
    // For credit cards, we might allow negative balance up to a limit
    if (type.toLowerCase() == 'credit_card') {
      return true; // Implement credit limit logic if needed
    }
    return balance >= amount;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account &&
        other.id == id &&
        other.userId == userId &&
        other.name == name &&
        other.type == type &&
        other.balance == balance;
  }

  @override
  int get hashCode {
    return Object.hash(id, userId, name, type, balance);
  }

  @override
  String toString() {
    return 'Account(id: $id, userId: $userId, name: $name, type: $type, balance: $balance)';
  }
}
