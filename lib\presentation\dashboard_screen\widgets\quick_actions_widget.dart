import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';

class QuickActionsWidget extends StatelessWidget {
  final VoidCallback onAddTransaction;
  final VoidCallback onManageAccounts;
  final VoidCallback onViewReports;

  const QuickActionsWidget({
    Key? key,
    required this.onAddTransaction,
    required this.onManageAccounts,
    required this.onViewReports,
  }) : super(key: key);

  Widget _buildActionButton({
    required String title,
    required String iconName,
    required Color color,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 2.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: CustomIconWidget(
                  iconName: iconName,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: isDark
                      ? AppTheme.textHighEmphasisDark
                      : AppTheme.textHighEmphasisLight,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: GoogleFonts.inter(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: isDark
                  ? AppTheme.textHighEmphasisDark
                  : AppTheme.textHighEmphasisLight,
            ),
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              _buildActionButton(
                title: 'Add Transaction',
                iconName: 'add',
                color: AppTheme.primaryPurple,
                onTap: onAddTransaction,
                isDark: isDark,
              ),
              SizedBox(width: 3.w),
              _buildActionButton(
                title: 'Manage Accounts',
                iconName: 'account_balance',
                color: AppTheme.primaryTeal,
                onTap: onManageAccounts,
                isDark: isDark,
              ),
              SizedBox(width: 3.w),
              _buildActionButton(
                title: 'View Reports',
                iconName: 'bar_chart',
                color: const Color(0xFFFFA726),
                onTap: onViewReports,
                isDark: isDark,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
