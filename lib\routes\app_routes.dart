import 'package:flutter/material.dart';
import '../presentation/splash_screen/splash_screen.dart';
import '../presentation/authentication_screen/authentication_screen.dart';
import '../presentation/dashboard_screen/dashboard_screen.dart';
import '../presentation/settings_screen/settings_screen.dart';
import '../presentation/add_transaction_screen/add_transaction_screen.dart';
import '../presentation/transaction_history_screen/transaction_history_screen.dart';
import '../presentation/accounts_screen/accounts_screen.dart';
import '../presentation/add_account_screen/add_account_screen.dart';
import '../presentation/coming_soon_screen/coming_soon_screen.dart';
import '../presentation/reports_screen/reports_screen.dart';
import '../presentation/offline_test_screen/offline_test_screen.dart';
import '../presentation/forgot_password_screen/forgot_password_screen.dart';
import '../presentation/reset_password_screen/reset_password_screen.dart';
import '../presentation/legal_pages/terms_of_service_screen.dart';
import '../presentation/legal_pages/privacy_policy_screen.dart';
import '../presentation/legal_pages/licenses_screen.dart';
import '../core/models/account_model.dart';

class AppRoutes {
  // TODO: Add your routes here
  static const String initial = '/';
  static const String splashScreen = '/splash-screen';
  static const String authenticationScreen = '/authentication-screen';
  static const String dashboardScreen = '/dashboard-screen';
  static const String settingsScreen = '/settings-screen';
  static const String addTransactionScreen = '/add-transaction-screen';
  static const String transactionHistoryScreen = '/transaction-history-screen';
  static const String accountsScreen = '/accounts-screen';
  static const String addAccountScreen = '/add-account-screen';
  static const String comingSoonAccounts = '/coming-soon-accounts';
  static const String reportsScreen = '/reports-screen';
  static const String offlineTestScreen = '/offline-test-screen';
  static const String forgotPasswordScreen = '/forgot-password-screen';
  static const String resetPasswordScreen = '/reset-password-screen';
  static const String termsOfServiceScreen = '/terms-of-service-screen';
  static const String privacyPolicyScreen = '/privacy-policy-screen';
  static const String licensesScreen = '/licenses-screen';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => const SplashScreen(),
    splashScreen: (context) => const SplashScreen(),
    authenticationScreen: (context) => const AuthenticationScreen(),
    dashboardScreen: (context) => const DashboardScreen(),
    settingsScreen: (context) => const SettingsScreen(),
    addTransactionScreen: (context) => const AddTransactionScreen(),
    transactionHistoryScreen: (context) => const TransactionHistoryScreen(),
    accountsScreen: (context) => const AccountsScreen(),
    addAccountScreen: (context) {
      final account = ModalRoute.of(context)?.settings.arguments as Account?;
      return AddAccountScreen(account: account);
    },
    comingSoonAccounts: (context) => const ComingSoonScreen(
      featureName: 'Accounts Management',
      description: 'Comprehensive account management features are arriving in the next update!',
      upcomingFeatures: [
        'Add multiple accounts (Bank, Cash, Credit Cards)',
        'Track account balances in real-time',
        'Manage account types and categories',
        'Transfer money between accounts',
        'View detailed account history',
        'Set account-specific budgets and goals',
      ],
    ),
    reportsScreen: (context) => const ReportsScreen(),
    offlineTestScreen: (context) => const OfflineTestScreen(),
    forgotPasswordScreen: (context) => const ForgotPasswordScreen(),
    resetPasswordScreen: (context) {
      final args = ModalRoute.of(context)?.settings.arguments as Map<String, String>?;
      return ResetPasswordScreen(
        token: args?['token'] ?? '',
        email: args?['email'] ?? '',
      );
    },
    termsOfServiceScreen: (context) => const TermsOfServiceScreen(),
    privacyPolicyScreen: (context) => const PrivacyPolicyScreen(),
    licensesScreen: (context) => const LicensesScreen(),
    // TODO: Add your other routes here
  };
}
