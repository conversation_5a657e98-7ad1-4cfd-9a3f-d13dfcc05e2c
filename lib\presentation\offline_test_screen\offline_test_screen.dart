import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../core/app_export.dart';
import '../../widgets/offline_indicator.dart';

/// Test screen for offline functionality
class OfflineTestScreen extends StatefulWidget {
  const OfflineTestScreen({Key? key}) : super(key: key);

  @override
  State<OfflineTestScreen> createState() => _OfflineTestScreenState();
}

class _OfflineTestScreenState extends State<OfflineTestScreen> {
  final ConnectivityService _connectivity = ConnectivityService.instance;
  final DataSyncService _syncService = DataSyncService.instance;
  final FinancialDataManager _dataManager = FinancialDataManager.instance;
  final OfflineStorageService _offlineStorage = OfflineStorageService.instance;

  @override
  void initState() {
    super.initState();
    _connectivity.addListener(_onConnectivityChanged);
    _syncService.addListener(_onSyncStateChanged);
    _dataManager.addListener(_onDataChanged);
  }

  @override
  void dispose() {
    _connectivity.removeListener(_onConnectivityChanged);
    _syncService.removeListener(_onSyncStateChanged);
    _dataManager.removeListener(_onDataChanged);
    super.dispose();
  }

  void _onConnectivityChanged() {
    if (mounted) setState(() {});
  }

  void _onSyncStateChanged() {
    if (mounted) setState(() {});
  }

  void _onDataChanged() {
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return OfflineIndicator(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Offline Test'),
          actions: [
            const CompactOfflineStatus(),
            const SyncStatusIndicator(),
            IconButton(
              onPressed: () => _connectivity.refreshConnectivityStatus(),
              icon: const Icon(Icons.refresh),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildConnectivityStatus(),
              SizedBox(height: 3.h),
              _buildSyncStatus(),
              SizedBox(height: 3.h),
              _buildDataStatus(),
              SizedBox(height: 3.h),
              _buildTestActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConnectivityStatus() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Connectivity Status',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 2.h),
            _buildStatusRow('Online', _connectivity.isOnline),
            _buildStatusRow('Has Connectivity', _connectivity.hasConnectivity),
            _buildStatusRow('Has Internet Access', _connectivity.hasInternetAccess),
            SizedBox(height: 1.h),
            Text(
              'Type: ${_connectivity.getConnectivityType()}',
              style: TextStyle(fontSize: 12.sp),
            ),
            if (_connectivity.lastConnectivityCheck != null)
              Text(
                'Last Check: ${_connectivity.lastConnectivityCheck!.toLocal()}',
                style: TextStyle(fontSize: 10.sp, color: Colors.grey),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncStatus() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sync Status',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 2.h),
            _buildStatusRow('Is Syncing', _syncService.isSyncing),
            _buildStatusRow('Has Unsynced Data', _syncService.hasUnsyncedData),
            SizedBox(height: 1.h),
            if (_syncService.isSyncing)
              LinearProgressIndicator(
                value: _syncService.syncProgress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryTeal),
              ),
            SizedBox(height: 1.h),
            Text(
              'Progress: ${(_syncService.syncProgress * 100).toInt()}%',
              style: TextStyle(fontSize: 12.sp),
            ),
            if (_syncService.lastSyncTime != null)
              Text(
                'Last Sync: ${_syncService.lastSyncTime!.toLocal()}',
                style: TextStyle(fontSize: 10.sp, color: Colors.grey),
              ),
            if (_syncService.syncError != null)
              Text(
                'Error: ${_syncService.syncError}',
                style: TextStyle(fontSize: 10.sp, color: Colors.red),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataStatus() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Status',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 2.h),
            _buildStatusRow('Is Loading', _dataManager.isLoading),
            _buildStatusRow('Is Offline Mode', _dataManager.isOfflineMode),
            SizedBox(height: 1.h),
            Text(
              'Transactions: ${_dataManager.transactions.length}',
              style: TextStyle(fontSize: 12.sp),
            ),
            Text(
              'Accounts: ${_dataManager.accounts.length}',
              style: TextStyle(fontSize: 12.sp),
            ),
            Text(
              'Categories: ${_dataManager.categories.length}',
              style: TextStyle(fontSize: 12.sp),
            ),
            if (_dataManager.lastUpdated != null)
              Text(
                'Last Updated: ${_dataManager.lastUpdated!.toLocal()}',
                style: TextStyle(fontSize: 10.sp, color: Colors.grey),
              ),
            if (_dataManager.error != null)
              Text(
                'Error: ${_dataManager.error}',
                style: TextStyle(fontSize: 10.sp, color: Colors.red),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestActions() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Actions',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 2.h),
            Wrap(
              spacing: 2.w,
              runSpacing: 1.h,
              children: [
                ElevatedButton(
                  onPressed: () => _connectivity.refreshConnectivityStatus(),
                  child: const Text('Refresh Connectivity'),
                ),
                ElevatedButton(
                  onPressed: () => _syncService.forceSyncNow(),
                  child: const Text('Force Sync'),
                ),
                ElevatedButton(
                  onPressed: () => _dataManager.initialize(),
                  child: const Text('Reload Data'),
                ),
                ElevatedButton(
                  onPressed: _testOfflineTransaction,
                  child: const Text('Add Test Transaction'),
                ),
                ElevatedButton(
                  onPressed: _clearOfflineData,
                  child: const Text('Clear Offline Data'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0.5.h),
      child: Row(
        children: [
          Icon(
            status ? Icons.check_circle : Icons.cancel,
            color: status ? Colors.green : Colors.red,
            size: 4.w,
          ),
          SizedBox(width: 2.w),
          Text(
            label,
            style: TextStyle(fontSize: 12.sp),
          ),
        ],
      ),
    );
  }

  Future<void> _testOfflineTransaction() async {
    final success = await _dataManager.addTransaction(
      type: 'expense',
      amount: 50.0,
      category: 'Food',
      account: 'Cash',
      description: 'Test offline transaction',
      date: DateTime.now(),
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Transaction added successfully' : 'Failed to add transaction'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _clearOfflineData() async {
    await _offlineStorage.clearUserData(1); // Assuming user ID 1 for test
    await _dataManager.initialize();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Offline data cleared'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
}
