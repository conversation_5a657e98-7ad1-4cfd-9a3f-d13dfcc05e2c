import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/account_card_widget.dart';
import './widgets/account_summary_widget.dart';
import './widgets/empty_accounts_widget.dart';

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({Key? key}) : super(key: key);

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen>
    with TickerProviderStateMixin {
  late final FinancialDataManager _financialDataManager;
  late AnimationController _refreshController;
  late Animation<double> _refreshAnimation;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _financialDataManager = FinancialDataManager.instance;
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _refreshAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _refreshController, curve: Curves.easeInOut),
    );
    _initializeData();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    if (!_isInitialized) {
      await _financialDataManager.initialize();
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    }
  }

  Future<void> _onRefresh() async {
    _refreshController.forward();
    await _financialDataManager.refreshAccounts();
    _refreshController.reset();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Accounts refreshed successfully'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _navigateToAddAccount() {
    Navigator.pushNamed(context, '/add-account-screen').then((_) {
      // Refresh accounts after returning from add account screen
      _financialDataManager.refreshAccounts();
    });
  }

  void _editAccount(Account account) {
    Navigator.pushNamed(
      context,
      '/add-account-screen',
      arguments: account,
    ).then((_) {
      // Refresh accounts after returning from edit account screen
      _financialDataManager.refreshAccounts();
    });
  }

  Future<void> _deleteAccount(Account account) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text(
          'Are you sure you want to delete "${account.name}"?\n\n'
          'This action cannot be undone and will affect all related transactions.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && account.id != null) {
      final success = await _financialDataManager.deleteAccount(account.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Account deleted successfully'
                : 'Failed to delete account'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }

  void _archiveAccount(Account account) async {
    if (account.id != null) {
      final success = await _financialDataManager.updateAccount(
        accountId: account.id!,
        isActive: false,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Account archived successfully'
                : 'Failed to archive account'),
            backgroundColor: success ? Colors.orange : Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ListenableBuilder(
      listenable: Listenable.merge([_financialDataManager, FormattingService.instance]),
      builder: (context, child) {
        final accounts = _financialDataManager.accounts;
        final totalBalance = _financialDataManager.totalBalance;

        return Scaffold(
          backgroundColor:
              isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
          appBar: _buildAppBar(),
          body: _buildBody(accounts, totalBalance),
          floatingActionButton: _buildFloatingActionButton(),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Text(
        'Accounts',
        style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        IconButton(
          onPressed: _onRefresh,
          icon: AnimatedBuilder(
            animation: _refreshAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _refreshAnimation.value * 2 * 3.14159,
                child: CustomIconWidget(
                  iconName: 'refresh',
                  color: AppTheme.primaryTeal,
                  size: 24,
                ),
              );
            },
          ),
        ),
        SizedBox(width: 2.w),
      ],
    );
  }

  Widget _buildBody(List<Account> accounts, double totalBalance) {
    if (!_isInitialized) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (accounts.isEmpty) {
      return const EmptyAccountsWidget();
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: CustomScrollView(
        slivers: [
          // Account Summary
          SliverToBoxAdapter(
            child: AccountSummaryWidget(
              totalBalance: totalBalance,
              accountCount: accounts.length,
            ),
          ),
          
          // Accounts List
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final account = accounts[index];
                  return AccountCardWidget(
                    account: account,
                    onTap: () => _editAccount(account),
                    onEdit: () => _editAccount(account),
                    onDelete: () => _deleteAccount(account),
                    onArchive: () => _archiveAccount(account),
                  );
                },
                childCount: accounts.length,
              ),
            ),
          ),
          
          // Bottom padding for FAB
          SliverToBoxAdapter(
            child: SizedBox(height: 10.h),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _navigateToAddAccount,
      backgroundColor: AppTheme.primaryTeal,
      foregroundColor: Colors.white,
      icon: CustomIconWidget(
        iconName: 'add',
        color: Colors.white,
        size: 24,
      ),
      label: Text(
        'Add Account',
        style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
