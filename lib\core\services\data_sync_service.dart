import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/transaction_model.dart' as TransactionModel;
import '../models/account_model.dart';
import '../models/category_model.dart';
import '../models/user_model.dart';
import '../models/sync_models.dart';
import '../models/api_response.dart';
import 'offline_storage_service.dart';
import 'connectivity_service.dart';
import 'http_client_service.dart';
import 'transaction_service.dart';
import 'account_service.dart';
import 'category_service.dart';
import 'auth_service.dart';

/// Data synchronization service for handling offline/online data sync
class DataSyncService extends ChangeNotifier {
  static DataSyncService? _instance;
  
  /// Singleton instance
  static DataSyncService get instance {
    _instance ??= DataSyncService._internal();
    return _instance!;
  }

  DataSyncService._internal() {
    _initialize();
  }

  // Services
  final OfflineStorageService _offlineStorage = OfflineStorageService.instance;
  final ConnectivityService _connectivity = ConnectivityService.instance;
  final HttpClientService _httpClient = HttpClientService.instance;
  final TransactionService _transactionService = TransactionService.instance;
  final AccountService _accountService = AccountService.instance;
  final CategoryService _categoryService = CategoryService.instance;

  // State
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  String? _syncError;
  int _syncProgress = 0;
  int _totalSyncItems = 0;
  Timer? _autoSyncTimer;
  StreamSubscription? _connectivitySubscription;

  // Getters
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;
  String? get syncError => _syncError;
  double get syncProgress => _totalSyncItems > 0 ? _syncProgress / _totalSyncItems : 0.0;
  bool get hasUnsyncedData => _totalSyncItems > 0;

  /// Check if there are pending sync items (for yellow dot indicator)
  Future<bool> get hasPendingChanges async {
    try {
      final pendingItems = await _offlineStorage.getPendingSyncItems();
      return pendingItems.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking pending changes: $e');
      return false;
    }
  }

  /// Initialize sync service
  void _initialize() {
    debugPrint('🔄 Initializing data sync service...');

    // Listen to connectivity changes
    _connectivity.addListener(_onConnectivityChanged);

    // Auto-sync disabled for manual sync only
    // _startAutoSync(); // Commented out for manual sync implementation
  }

  /// Handle connectivity changes
  void _onConnectivityChanged() {
    if (_connectivity.isOnline) {
      debugPrint('🔄 Connection restored, sync available for manual trigger');
      // Notify listeners that sync is now available
      notifyListeners();
    }
  }

  /// Start automatic sync timer
  void _startAutoSync() {
    _autoSyncTimer?.cancel();
    _autoSyncTimer = Timer.periodic(
      const Duration(minutes: 5), // Sync every 5 minutes when online
      (timer) {
        if (_connectivity.isOnline && !_isSyncing) {
          syncData();
        }
      },
    );
  }

  /// Main sync method
  Future<bool> syncData({int? userId}) async {
    if (_isSyncing) {
      debugPrint('🔄 Sync already in progress, skipping...');
      return false;
    }

    if (!_connectivity.isOnline) {
      debugPrint('🔄 No internet connection, sync skipped');
      return false;
    }

    try {
      _setSyncState(true, null);
      debugPrint('🔄 Starting data synchronization...');

      // Get user ID if not provided
      userId ??= await _getCurrentUserId();
      if (userId == null) {
        throw Exception('No user logged in');
      }

      // Step 1: Download data from server
      await _downloadDataFromServer(userId);

      // Step 2: Upload pending changes to server
      await _uploadPendingChanges(userId);

      // Step 3: Resolve conflicts
      await _resolveConflicts(userId);

      _lastSyncTime = DateTime.now();
      _setSyncState(false, null);
      
      debugPrint('✅ Data synchronization completed successfully');
      return true;

    } catch (e) {
      debugPrint('❌ Sync error: $e');
      _setSyncState(false, e.toString());
      return false;
    }
  }

  /// Download data from server and update local storage
  Future<void> _downloadDataFromServer(int userId) async {
    debugPrint('🔄 Downloading data from server...');

    try {
      // Download transactions
      final transactionsResponse = await _transactionService.getTransactions();
      if (transactionsResponse.isSuccess && transactionsResponse.data != null) {
        for (final transaction in transactionsResponse.data!) {
          await _offlineStorage.saveTransaction(transaction, syncStatus: SyncStatus.synced);
        }
        debugPrint('✅ Downloaded ${transactionsResponse.data!.length} transactions');
      }

      // Download accounts
      final accountsResponse = await _accountService.getAccounts();
      if (accountsResponse.isSuccess && accountsResponse.data != null) {
        for (final account in accountsResponse.data!) {
          await _offlineStorage.saveAccount(account, syncStatus: SyncStatus.synced);
        }
        debugPrint('✅ Downloaded ${accountsResponse.data!.length} accounts');
      }

      // Download categories
      final categoriesResponse = await _categoryService.getCategories();
      if (categoriesResponse.isSuccess && categoriesResponse.data != null) {
        for (final category in categoriesResponse.data!) {
          await _offlineStorage.saveCategory(category, syncStatus: SyncStatus.synced);
        }
        debugPrint('✅ Downloaded ${categoriesResponse.data!.length} categories');
      }

    } catch (e) {
      debugPrint('❌ Error downloading data from server: $e');
      rethrow;
    }
  }

  /// Upload pending changes to server
  Future<void> _uploadPendingChanges(int userId) async {
    debugPrint('🔄 Uploading pending changes to server...');

    final pendingItems = await _offlineStorage.getPendingSyncItems();
    _totalSyncItems = pendingItems.length;
    _syncProgress = 0;

    for (final item in pendingItems) {
      try {
        await _processSyncQueueItem(item);
        await _offlineStorage.removeFromSyncQueue(item.id!);
        _syncProgress++;
        notifyListeners();
      } catch (e) {
        debugPrint('❌ Error processing sync item ${item.id}: $e');
        
        // Update retry count
        final newRetryCount = item.retryCount + 1;
        if (newRetryCount >= 3) {
          // Mark as failed after 3 retries
          debugPrint('❌ Sync item ${item.id} failed after 3 retries');
          await _offlineStorage.removeFromSyncQueue(item.id!);
        } else {
          await _offlineStorage.updateSyncQueueRetry(item.id!, newRetryCount);
        }
      }
    }

    debugPrint('✅ Uploaded ${_syncProgress} pending changes');
  }

  /// Process individual sync queue item
  Future<void> _processSyncQueueItem(SyncQueueItem item) async {
    switch (item.tableName) {
      case 'transactions':
        await _syncTransaction(item);
        break;
      case 'accounts':
        await _syncAccount(item);
        break;
      case 'categories':
        await _syncCategory(item);
        break;
      default:
        debugPrint('❌ Unknown table name in sync queue: ${item.tableName}');
    }
  }

  /// Sync transaction item
  Future<void> _syncTransaction(SyncQueueItem item) async {
    final transaction = TransactionModel.Transaction.fromJson(item.data);

    switch (item.operation) {
      case SyncOperation.create:
        final response = await _transactionService.createTransaction(
          type: transaction.type,
          amount: transaction.amount,
          category: transaction.category,
          account: transaction.account,
          description: transaction.description,
          date: transaction.date,
          subcategory: transaction.subcategory,
          notes: transaction.notes,
          isRecurring: transaction.isRecurring,
          recurringFrequency: transaction.recurringFrequency,
          recurringEndDate: transaction.recurringEndDate,
          tags: transaction.tags,
          location: transaction.location,
        );
        if (!response.isSuccess) {
          throw Exception('Failed to create transaction: ${response.message}');
        }

        // Update local transaction with server ID if it was a temporary ID
        if (response.data != null && transaction.id != null && transaction.id! < 0) {
          await _updateLocalTransactionWithServerId(transaction.id!, response.data!);
        }
        break;
        
      case SyncOperation.update:
        final response = await _transactionService.updateTransaction(
          transactionId: transaction.id!,
          type: transaction.type,
          amount: transaction.amount,
          category: transaction.category,
          account: transaction.account,
          description: transaction.description,
          date: transaction.date,
          subcategory: transaction.subcategory,
          notes: transaction.notes,
          isRecurring: transaction.isRecurring,
          recurringFrequency: transaction.recurringFrequency,
          recurringEndDate: transaction.recurringEndDate,
          tags: transaction.tags,
          location: transaction.location,
        );
        if (!response.isSuccess) {
          throw Exception('Failed to update transaction: ${response.message}');
        }
        break;
        
      case SyncOperation.delete:
        final response = await _transactionService.deleteTransaction(transaction.id!);
        if (!response.isSuccess) {
          throw Exception('Failed to delete transaction: ${response.message}');
        }
        break;
    }
  }

  /// Sync account item
  Future<void> _syncAccount(SyncQueueItem item) async {
    final account = Account.fromJson(item.data);
    
    switch (item.operation) {
      case SyncOperation.create:
        final response = await _accountService.createAccount(
          name: account.name,
          type: account.type,
          initialBalance: account.balance,
          bankName: account.bankName,
          accountNumber: account.accountNumber,
          icon: account.icon ?? 'account_balance_wallet',
          color: account.color ?? '#2196F3',
        );
        if (!response.isSuccess) {
          throw Exception('Failed to create account: ${response.message}');
        }
        break;
        
      case SyncOperation.update:
        // Account updates would need to be implemented in AccountService
        debugPrint('⚠️ Account update sync not implemented yet');
        break;
        
      case SyncOperation.delete:
        // Account deletion would need to be implemented in AccountService
        debugPrint('⚠️ Account delete sync not implemented yet');
        break;
    }
  }

  /// Sync category item
  Future<void> _syncCategory(SyncQueueItem item) async {
    // Category sync would need to be implemented based on CategoryService capabilities
    debugPrint('⚠️ Category sync not implemented yet');
  }

  /// Resolve sync conflicts
  Future<void> _resolveConflicts(int userId) async {
    debugPrint('🔄 Resolving sync conflicts...');
    // Conflict resolution logic would be implemented here
    // For now, we'll use a simple "server wins" strategy
  }

  /// Update local transaction with server ID
  Future<void> _updateLocalTransactionWithServerId(int tempId, TransactionModel.Transaction serverTransaction) async {
    try {
      // Update in offline storage
      await _offlineStorage.saveTransaction(serverTransaction, syncStatus: SyncStatus.synced);

      // Remove the old temporary transaction
      if (tempId < 0) {
        // Delete the temporary transaction from local storage
        await _offlineStorage.deleteTransaction(tempId, syncStatus: SyncStatus.synced);
      }

      debugPrint('✅ Updated local transaction: temp ID $tempId → server ID ${serverTransaction.id}');
    } catch (e) {
      debugPrint('❌ Error updating local transaction with server ID: $e');
    }
  }

  /// Get current user ID
  Future<int?> _getCurrentUserId() async {
    try {
      final user = await AuthService.instance.getCurrentUser();
      return user?.id;
    } catch (e) {
      debugPrint('❌ Error getting current user ID: $e');
      return null;
    }
  }

  /// Set sync state and notify listeners
  void _setSyncState(bool syncing, String? error) {
    _isSyncing = syncing;
    _syncError = error;
    if (!syncing) {
      _syncProgress = 0;
      _totalSyncItems = 0;
    }
    notifyListeners();
  }

  /// Force sync now
  Future<bool> forceSyncNow() async {
    return await syncData();
  }

  /// Add transaction to sync queue
  Future<void> queueTransactionSync(TransactionModel.Transaction transaction, SyncOperation operation) async {
    final queueItem = SyncQueueItem(
      tableName: 'transactions',
      operation: operation,
      recordId: transaction.id,
      data: transaction.toJson(),
      createdAt: DateTime.now(),
    );

    await _offlineStorage.addToSyncQueue(queueItem);

    // Notify listeners that there are pending changes (for yellow dot indicator)
    notifyListeners();
  }

  /// Add account to sync queue
  Future<void> queueAccountSync(Account account, SyncOperation operation) async {
    final queueItem = SyncQueueItem(
      tableName: 'accounts',
      operation: operation,
      recordId: account.id,
      data: account.toJson(),
      createdAt: DateTime.now(),
    );
    
    await _offlineStorage.addToSyncQueue(queueItem);

    // Notify listeners that there are pending changes (for yellow dot indicator)
    notifyListeners();
  }

  /// Dispose resources
  @override
  void dispose() {
    debugPrint('🔄 Disposing data sync service...');
    _autoSyncTimer?.cancel();
    _connectivity.removeListener(_onConnectivityChanged);
    super.dispose();
  }
}
