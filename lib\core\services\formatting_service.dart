import 'package:flutter/foundation.dart';
import 'settings_service.dart';

/// Service for formatting data according to user preferences
class FormattingService extends ChangeNotifier {
  static FormattingService? _instance;
  final SettingsService _settingsService = SettingsService.instance;

  /// Singleton instance
  static FormattingService get instance {
    _instance ??= FormattingService._internal();
    return _instance!;
  }

  FormattingService._internal() {
    _settingsService.addListener(_onSettingsChanged);
  }

  /// Handle settings changes
  void _onSettingsChanged() {
    notifyListeners();
  }

  /// Format currency amount with symbol
  String formatCurrency(double amount) {
    final currency = _settingsService.currency;
    final formattedNumber = formatNumber(amount);
    return '${currency.symbol}$formattedNumber';
  }

  /// Format currency amount without symbol
  String formatAmount(double amount) {
    return formatNumber(amount);
  }

  /// Format number according to selected format
  String formatNumber(double number) {
    switch (_settingsService.numberFormat) {
      case NumberFormatType.indian:
        return _formatIndianNumber(number);
      case NumberFormatType.international:
        return _formatInternationalNumber(number);
      case NumberFormatType.european:
        return _formatEuropeanNumber(number);
    }
  }

  /// Format date according to selected format
  String formatDate(DateTime date) {
    switch (_settingsService.dateFormat) {
      case DateFormatType.ddMmYyyy:
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      case DateFormatType.mmDdYyyy:
        return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
      case DateFormatType.yyyyMmDd:
        return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      case DateFormatType.ddMmmYyyy:
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return '${date.day.toString().padLeft(2, '0')} ${months[date.month - 1]} ${date.year}';
    }
  }

  /// Format date with time
  String formatDateTime(DateTime dateTime) {
    final dateStr = formatDate(dateTime);
    final timeStr = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    return '$dateStr $timeStr';
  }

  /// Format date for display (shorter format)
  String formatDateShort(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == yesterday) {
      return 'Yesterday';
    } else if (now.difference(date).inDays < 7) {
      const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return weekdays[date.weekday - 1];
    } else {
      return formatDate(date);
    }
  }

  /// Get current currency symbol
  String get currencySymbol => _settingsService.currency.symbol;

  /// Get current currency code
  String get currencyCode => _settingsService.currency.code;

  /// Get current currency name
  String get currencyName => _settingsService.currency.name;

  /// Format Indian number system (1,23,456.78)
  String _formatIndianNumber(double number) {
    final parts = number.toStringAsFixed(2).split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    if (integerPart.length <= 3) {
      return '$integerPart.${decimalPart}';
    }
    
    String result = integerPart.substring(integerPart.length - 3);
    String remaining = integerPart.substring(0, integerPart.length - 3);
    
    while (remaining.length > 2) {
      result = '${remaining.substring(remaining.length - 2)},$result';
      remaining = remaining.substring(0, remaining.length - 2);
    }
    
    if (remaining.isNotEmpty) {
      result = '$remaining,$result';
    }
    
    return '$result.${decimalPart}';
  }

  /// Format international number system (123,456.78)
  String _formatInternationalNumber(double number) {
    final parts = number.toStringAsFixed(2).split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    String result = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        result += ',';
      }
      result += integerPart[i];
    }
    
    return '$result.${decimalPart}';
  }

  /// Format European number system (123.456,78)
  String _formatEuropeanNumber(double number) {
    final parts = number.toStringAsFixed(2).split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    String result = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        result += '.';
      }
      result += integerPart[i];
    }
    
    return '$result,${decimalPart}';
  }

  /// Parse formatted number back to double
  double parseNumber(String formattedNumber) {
    // Remove currency symbols and spaces
    String cleanNumber = formattedNumber
        .replaceAll(_settingsService.currency.symbol, '')
        .replaceAll(' ', '')
        .trim();

    switch (_settingsService.numberFormat) {
      case NumberFormatType.indian:
      case NumberFormatType.international:
        // Remove commas and parse
        cleanNumber = cleanNumber.replaceAll(',', '');
        break;
      case NumberFormatType.european:
        // Replace comma with dot for decimal, remove dots for thousands
        if (cleanNumber.contains(',')) {
          final parts = cleanNumber.split(',');
          if (parts.length == 2) {
            cleanNumber = '${parts[0].replaceAll('.', '')}.${parts[1]}';
          }
        } else {
          cleanNumber = cleanNumber.replaceAll('.', '');
        }
        break;
    }

    return double.tryParse(cleanNumber) ?? 0.0;
  }

  /// Format percentage
  String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Format large numbers with K, M, B suffixes
  String formatCompactNumber(double number) {
    if (number.abs() >= 1000000000) {
      return '${(number / 1000000000).toStringAsFixed(1)}B';
    } else if (number.abs() >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number.abs() >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return formatNumber(number);
    }
  }

  /// Format compact currency
  String formatCompactCurrency(double amount) {
    final compactNumber = formatCompactNumber(amount);
    return '${_settingsService.currency.symbol}$compactNumber';
  }

  @override
  void dispose() {
    _settingsService.removeListener(_onSettingsChanged);
    super.dispose();
  }
}
