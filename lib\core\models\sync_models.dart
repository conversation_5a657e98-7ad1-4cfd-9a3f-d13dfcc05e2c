import 'dart:convert';

/// Enum for sync status
enum SyncStatus {
  synced,
  pending,
  failed,
  conflict
}

/// Extension for SyncStatus enum
extension SyncStatusExtension on SyncStatus {
  String get value {
    switch (this) {
      case SyncStatus.synced:
        return 'synced';
      case SyncStatus.pending:
        return 'pending';
      case SyncStatus.failed:
        return 'failed';
      case SyncStatus.conflict:
        return 'conflict';
    }
  }

  static SyncStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'synced':
        return SyncStatus.synced;
      case 'pending':
        return SyncStatus.pending;
      case 'failed':
        return SyncStatus.failed;
      case 'conflict':
        return SyncStatus.conflict;
      default:
        return SyncStatus.synced;
    }
  }
}

/// Enum for sync operations
enum SyncOperation {
  create,
  update,
  delete
}

/// Extension for SyncOperation enum
extension SyncOperationExtension on SyncOperation {
  String get value {
    switch (this) {
      case SyncOperation.create:
        return 'create';
      case SyncOperation.update:
        return 'update';
      case SyncOperation.delete:
        return 'delete';
    }
  }

  static SyncOperation fromString(String value) {
    switch (value.toLowerCase()) {
      case 'create':
        return SyncOperation.create;
      case 'update':
        return SyncOperation.update;
      case 'delete':
        return SyncOperation.delete;
      default:
        return SyncOperation.create;
    }
  }
}

/// Base class for syncable models
abstract class SyncableModel {
  final int? id;
  final DateTime? lastSynced;
  final SyncStatus syncStatus;

  const SyncableModel({
    this.id,
    this.lastSynced,
    this.syncStatus = SyncStatus.synced,
  });

  /// Convert to JSON for sync
  Map<String, dynamic> toSyncJson();
  
  /// Get table name for sync operations
  String get tableName;
  
  /// Check if model needs sync
  bool get needsSync => syncStatus != SyncStatus.synced;
  
  /// Check if model has conflicts
  bool get hasConflict => syncStatus == SyncStatus.conflict;
}

/// Sync queue item for pending operations
class SyncQueueItem {
  final int? id;
  final String tableName;
  final SyncOperation operation;
  final int? recordId;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final int retryCount;

  const SyncQueueItem({
    this.id,
    required this.tableName,
    required this.operation,
    this.recordId,
    required this.data,
    required this.createdAt,
    this.retryCount = 0,
  });

  /// Create from JSON
  factory SyncQueueItem.fromJson(Map<String, dynamic> json) {
    return SyncQueueItem(
      id: json['id'],
      tableName: json['table_name'],
      operation: SyncOperationExtension.fromString(json['operation']),
      recordId: json['record_id'],
      data: json['data'] is String 
          ? jsonDecode(json['data']) 
          : json['data'],
      createdAt: DateTime.parse(json['created_at']),
      retryCount: json['retry_count'] ?? 0,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'table_name': tableName,
      'operation': operation.value,
      'record_id': recordId,
      'data': jsonEncode(data),
      'created_at': createdAt.toIso8601String(),
      'retry_count': retryCount,
    };
  }

  /// Create a copy with updated retry count
  SyncQueueItem copyWithRetry() {
    return SyncQueueItem(
      id: id,
      tableName: tableName,
      operation: operation,
      recordId: recordId,
      data: data,
      createdAt: createdAt,
      retryCount: retryCount + 1,
    );
  }

  @override
  String toString() {
    return 'SyncQueueItem(id: $id, table: $tableName, operation: ${operation.value}, recordId: $recordId, retryCount: $retryCount)';
  }
}

/// Sync conflict resolution strategy
enum ConflictResolution {
  useLocal,
  useRemote,
  merge,
  manual
}

/// Sync conflict information
class SyncConflict {
  final String tableName;
  final int recordId;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> remoteData;
  final DateTime conflictDetectedAt;
  final ConflictResolution? resolution;

  const SyncConflict({
    required this.tableName,
    required this.recordId,
    required this.localData,
    required this.remoteData,
    required this.conflictDetectedAt,
    this.resolution,
  });

  /// Create from JSON
  factory SyncConflict.fromJson(Map<String, dynamic> json) {
    return SyncConflict(
      tableName: json['table_name'],
      recordId: json['record_id'],
      localData: json['local_data'] is String 
          ? jsonDecode(json['local_data']) 
          : json['local_data'],
      remoteData: json['remote_data'] is String 
          ? jsonDecode(json['remote_data']) 
          : json['remote_data'],
      conflictDetectedAt: DateTime.parse(json['conflict_detected_at']),
      resolution: json['resolution'] != null 
          ? ConflictResolution.values.firstWhere(
              (e) => e.toString().split('.').last == json['resolution']
            )
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'table_name': tableName,
      'record_id': recordId,
      'local_data': jsonEncode(localData),
      'remote_data': jsonEncode(remoteData),
      'conflict_detected_at': conflictDetectedAt.toIso8601String(),
      'resolution': resolution?.toString().split('.').last,
    };
  }

  @override
  String toString() {
    return 'SyncConflict(table: $tableName, recordId: $recordId, resolution: $resolution)';
  }
}

/// Sync statistics
class SyncStats {
  final int totalRecords;
  final int syncedRecords;
  final int pendingRecords;
  final int failedRecords;
  final int conflictRecords;
  final DateTime? lastSyncTime;
  final Duration? lastSyncDuration;

  const SyncStats({
    required this.totalRecords,
    required this.syncedRecords,
    required this.pendingRecords,
    required this.failedRecords,
    required this.conflictRecords,
    this.lastSyncTime,
    this.lastSyncDuration,
  });

  /// Calculate sync progress percentage
  double get syncProgress {
    if (totalRecords == 0) return 1.0;
    return syncedRecords / totalRecords;
  }

  /// Check if all records are synced
  bool get isFullySynced => pendingRecords == 0 && failedRecords == 0 && conflictRecords == 0;

  /// Get human readable sync status
  String get statusDescription {
    if (isFullySynced) return 'All data synced';
    if (conflictRecords > 0) return '$conflictRecords conflicts need resolution';
    if (failedRecords > 0) return '$failedRecords failed to sync';
    if (pendingRecords > 0) return '$pendingRecords pending sync';
    return 'Unknown status';
  }

  @override
  String toString() {
    return 'SyncStats(total: $totalRecords, synced: $syncedRecords, pending: $pendingRecords, failed: $failedRecords, conflicts: $conflictRecords)';
  }
}
