import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';

/// API Configuration class that handles environment variables and API endpoints
class ApiConfig {
  static late Map<String, dynamic> _config;
  static bool _isInitialized = false;

  /// Initialize the configuration by loading from env.json
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final String response = await rootBundle.loadString('env.json');
      _config = json.decode(response);
      _isInitialized = true;
    } catch (e) {
      // Fallback configuration if env.json is not found
      _config = {
        'API_BASE_URL': 'https://expenseflow.incrediblemarathi.com/api',
      };
      _isInitialized = true;
    }
  }

  /// Get the base API URL
  static String get baseUrl {
    _ensureInitialized();
    return _config['API_BASE_URL'] ?? 'https://expenseflow.incrediblemarathi.com/api';
  }

  /// API Endpoints
  static const String testEndpoint = '/test';
  static const String registerEndpoint = '/register';
  static const String loginEndpoint = '/login';
  static const String logoutEndpoint = '/logout';
  static const String profileEndpoint = '/profile';
  static const String changePasswordEndpoint = '/change-password.php';
  static const String forgotPasswordEndpoint = '/forgot-password.php';
  static const String resetPasswordEndpoint = '/reset-password.php';

  // Financial Data Endpoints
  static const String transactionsEndpoint = '/transactions.php';
  static const String accountsEndpoint = '/accounts.php';
  static const String categoriesEndpoint = '/categories.php';
  static const String financialSummaryEndpoint = '/financial-summary.php';
  static const String dashboardEndpoint = '/dashboard.php';

  /// HTTP Headers
  static const String contentTypeHeader = 'Content-Type';
  static const String authorizationHeader = 'Authorization';
  static const String acceptHeader = 'Accept';
  static const String userAgentHeader = 'User-Agent';

  /// HTTP Status Codes
  static const int statusOk = 200;
  static const int statusCreated = 201;
  static const int statusBadRequest = 400;
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusUnprocessableEntity = 422;
  static const int statusInternalServerError = 500;

  /// Request timeouts (in milliseconds)
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  /// Storage Keys for SharedPreferences
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';
  static const String tokenExpiryKey = 'token_expiry';

  /// Ensure configuration is initialized
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('ApiConfig not initialized. Call ApiConfig.initialize() first.');
    }
  }

  /// Get full URL for an endpoint
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }

  /// Check if running in debug mode
  static bool get isDebugMode {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  /// Get platform-specific user agent
  static String get userAgent {
    if (Platform.isAndroid) {
      return 'FinTrack-Android/1.0.0';
    } else if (Platform.isIOS) {
      return 'FinTrack-iOS/1.0.0';
    } else {
      return 'FinTrack/1.0.0';
    }
  }
}
