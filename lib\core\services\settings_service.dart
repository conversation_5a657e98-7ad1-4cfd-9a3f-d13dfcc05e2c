import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// Enum for theme modes
enum AppThemeMode {
  light,
  dark,
  system
}

/// Enum for supported currencies
enum SupportedCurrency {
  inr('INR', '₹', 'Indian Rupee'),
  usd('USD', '\$', 'US Dollar'),
  eur('EUR', '€', 'Euro'),
  gbp('GBP', '£', 'British Pound'),
  jpy('JPY', '¥', 'Japanese Yen'),
  cad('CAD', 'C\$', 'Canadian Dollar'),
  aud('AUD', 'A\$', 'Australian Dollar');

  const SupportedCurrency(this.code, this.symbol, this.name);
  final String code;
  final String symbol;
  final String name;
}

/// Enum for date formats
enum DateFormatType {
  ddMmYyyy('DD/MM/YYYY'),
  mmDdYyyy('MM/DD/YYYY'),
  yyyyMmDd('YYYY-MM-DD'),
  ddMmmYyyy('DD MMM YYYY');

  const DateFormatType(this.format);
  final String format;
}

/// Enum for number formats
enum NumberFormatType {
  indian('Indian (1,00,000)'),
  international('International (100,000)'),
  european('European (100.000,00)');

  const NumberFormatType(this.format);
  final String format;
}

/// Settings service for managing app preferences
class SettingsService extends ChangeNotifier {
  static SettingsService? _instance;
  SharedPreferences? _prefs;

  /// Singleton instance
  static SettingsService get instance {
    _instance ??= SettingsService._internal();
    return _instance!;
  }

  SettingsService._internal();

  // Settings keys
  static const String _themeKey = 'app_theme_mode';
  static const String _currencyKey = 'app_currency';
  static const String _dateFormatKey = 'app_date_format';
  static const String _numberFormatKey = 'app_number_format';
  static const String _biometricKey = 'biometric_enabled';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _autoBackupKey = 'auto_backup_enabled';

  // Current settings
  AppThemeMode _themeMode = AppThemeMode.system;
  SupportedCurrency _currency = SupportedCurrency.inr;
  DateFormatType _dateFormat = DateFormatType.ddMmYyyy;
  NumberFormatType _numberFormat = NumberFormatType.indian;
  bool _biometricEnabled = false;
  bool _notificationsEnabled = true;
  bool _autoBackupEnabled = true;

  // Getters
  AppThemeMode get themeMode => _themeMode;
  SupportedCurrency get currency => _currency;
  DateFormatType get dateFormat => _dateFormat;
  NumberFormatType get numberFormat => _numberFormat;
  bool get biometricEnabled => _biometricEnabled;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get autoBackupEnabled => _autoBackupEnabled;

  /// Initialize settings service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSettings();
    debugPrint('✅ Settings service initialized');
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      // Load theme mode
      final themeIndex = _prefs?.getInt(_themeKey) ?? AppThemeMode.system.index;
      _themeMode = AppThemeMode.values[themeIndex];

      // Load currency
      final currencyCode = _prefs?.getString(_currencyKey) ?? SupportedCurrency.inr.code;
      _currency = SupportedCurrency.values.firstWhere(
        (c) => c.code == currencyCode,
        orElse: () => SupportedCurrency.inr,
      );

      // Load date format
      final dateFormatIndex = _prefs?.getInt(_dateFormatKey) ?? DateFormatType.ddMmYyyy.index;
      _dateFormat = DateFormatType.values[dateFormatIndex];

      // Load number format
      final numberFormatIndex = _prefs?.getInt(_numberFormatKey) ?? NumberFormatType.indian.index;
      _numberFormat = NumberFormatType.values[numberFormatIndex];

      // Load boolean settings
      _biometricEnabled = _prefs?.getBool(_biometricKey) ?? false;
      _notificationsEnabled = _prefs?.getBool(_notificationsKey) ?? true;
      _autoBackupEnabled = _prefs?.getBool(_autoBackupKey) ?? true;

      debugPrint('✅ Settings loaded: theme=${_themeMode.name}, currency=${_currency.code}');
    } catch (e) {
      debugPrint('❌ Error loading settings: $e');
    }
  }

  /// Set theme mode
  Future<void> setThemeMode(AppThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _prefs?.setInt(_themeKey, mode.index);
      notifyListeners();
      debugPrint('✅ Theme mode changed to: ${mode.name}');
    }
  }

  /// Set currency
  Future<void> setCurrency(SupportedCurrency currency) async {
    if (_currency != currency) {
      _currency = currency;
      await _prefs?.setString(_currencyKey, currency.code);
      notifyListeners();
      debugPrint('✅ Currency changed to: ${currency.code}');
    }
  }

  /// Set date format
  Future<void> setDateFormat(DateFormatType format) async {
    if (_dateFormat != format) {
      _dateFormat = format;
      await _prefs?.setInt(_dateFormatKey, format.index);
      notifyListeners();
      debugPrint('✅ Date format changed to: ${format.format}');
    }
  }

  /// Set number format
  Future<void> setNumberFormat(NumberFormatType format) async {
    if (_numberFormat != format) {
      _numberFormat = format;
      await _prefs?.setInt(_numberFormatKey, format.index);
      notifyListeners();
      debugPrint('✅ Number format changed to: ${format.format}');
    }
  }

  /// Set biometric authentication
  Future<void> setBiometricEnabled(bool enabled) async {
    if (_biometricEnabled != enabled) {
      _biometricEnabled = enabled;
      await _prefs?.setBool(_biometricKey, enabled);
      notifyListeners();
      debugPrint('✅ Biometric authentication: ${enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// Set notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled != enabled) {
      _notificationsEnabled = enabled;
      await _prefs?.setBool(_notificationsKey, enabled);
      notifyListeners();
      debugPrint('✅ Notifications: ${enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// Set auto backup
  Future<void> setAutoBackupEnabled(bool enabled) async {
    if (_autoBackupEnabled != enabled) {
      _autoBackupEnabled = enabled;
      await _prefs?.setBool(_autoBackupKey, enabled);
      notifyListeners();
      debugPrint('✅ Auto backup: ${enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// Get theme mode for Flutter
  ThemeMode get flutterThemeMode {
    switch (_themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  /// Format currency amount
  String formatCurrency(double amount) {
    final formattedNumber = formatNumber(amount);
    return '${_currency.symbol}$formattedNumber';
  }

  /// Format number according to selected format
  String formatNumber(double number) {
    switch (_numberFormat) {
      case NumberFormatType.indian:
        return _formatIndianNumber(number);
      case NumberFormatType.international:
        return _formatInternationalNumber(number);
      case NumberFormatType.european:
        return _formatEuropeanNumber(number);
    }
  }

  /// Format date according to selected format
  String formatDate(DateTime date) {
    switch (_dateFormat) {
      case DateFormatType.ddMmYyyy:
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      case DateFormatType.mmDdYyyy:
        return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
      case DateFormatType.yyyyMmDd:
        return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      case DateFormatType.ddMmmYyyy:
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return '${date.day.toString().padLeft(2, '0')} ${months[date.month - 1]} ${date.year}';
    }
  }

  /// Format Indian number system
  String _formatIndianNumber(double number) {
    final parts = number.toStringAsFixed(2).split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    if (integerPart.length <= 3) {
      return '$integerPart.${decimalPart}';
    }
    
    String result = integerPart.substring(integerPart.length - 3);
    String remaining = integerPart.substring(0, integerPart.length - 3);
    
    while (remaining.length > 2) {
      result = '${remaining.substring(remaining.length - 2)},$result';
      remaining = remaining.substring(0, remaining.length - 2);
    }
    
    if (remaining.isNotEmpty) {
      result = '$remaining,$result';
    }
    
    return '$result.${decimalPart}';
  }

  /// Format international number system
  String _formatInternationalNumber(double number) {
    final parts = number.toStringAsFixed(2).split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    String result = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        result += ',';
      }
      result += integerPart[i];
    }
    
    return '$result.${decimalPart}';
  }

  /// Format European number system
  String _formatEuropeanNumber(double number) {
    final parts = number.toStringAsFixed(2).split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    String result = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        result += '.';
      }
      result += integerPart[i];
    }
    
    return '$result,${decimalPart}';
  }
}
