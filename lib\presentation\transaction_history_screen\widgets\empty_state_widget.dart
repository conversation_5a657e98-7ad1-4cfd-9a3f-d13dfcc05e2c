import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class EmptyStateWidget extends StatelessWidget {
  final String type;
  final VoidCallback? onAction;

  const EmptyStateWidget({
    super.key,
    required this.type,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(8.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppTheme.primaryTeal.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20.w),
              ),
              child: Center(
                child: CustomIconWidget(
                  iconName: _getIconName(),
                  color: AppTheme.primaryTeal,
                  size: 20.w,
                ),
              ),
            ),

            SizedBox(height: 4.h),

            // Title
            Text(
              _getTitle(),
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDark
                        ? AppTheme.textHighEmphasisDark
                        : AppTheme.textHighEmphasisLight,
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 2.h),

            // Description
            Text(
              _getDescription(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isDark
                        ? AppTheme.textMediumEmphasisDark
                        : AppTheme.textMediumEmphasisLight,
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 4.h),

            // Action button
            if (onAction != null && _getActionText().isNotEmpty)
              ElevatedButton(
                onPressed: onAction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryTeal,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomIconWidget(
                      iconName: _getActionIcon(),
                      color: Colors.white,
                      size: 5.w,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      _getActionText(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getIconName() {
    switch (type) {
      case 'no_transactions':
        return 'receipt_long';
      case 'no_search_results':
        return 'search_off';
      case 'no_filtered_results':
        return 'filter_list_off';
      default:
        return 'inbox';
    }
  }

  String _getTitle() {
    switch (type) {
      case 'no_transactions':
        return 'No Transactions Yet';
      case 'no_search_results':
        return 'No Search Results';
      case 'no_filtered_results':
        return 'No Filtered Results';
      default:
        return 'Nothing Here';
    }
  }

  String _getDescription() {
    switch (type) {
      case 'no_transactions':
        return 'Start tracking your finances by adding your first transaction. Tap the + button to get started.';
      case 'no_search_results':
        return 'We couldn\'t find any transactions matching your search. Try different keywords or check your spelling.';
      case 'no_filtered_results':
        return 'No transactions match your current filters. Try adjusting your filter criteria or clear all filters.';
      default:
        return 'There\'s nothing to show here right now.';
    }
  }

  String _getActionText() {
    switch (type) {
      case 'no_transactions':
        return 'Add Transaction';
      case 'no_search_results':
        return '';
      case 'no_filtered_results':
        return 'Clear Filters';
      default:
        return '';
    }
  }

  String _getActionIcon() {
    switch (type) {
      case 'no_transactions':
        return 'add';
      case 'no_filtered_results':
        return 'clear_all';
      default:
        return 'add';
    }
  }
}
