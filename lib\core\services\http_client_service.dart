import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';
import 'token_storage_service.dart';

/// HTTP Client Service using Dio for API communication
class HttpClientService {
  static HttpClientService? _instance;
  late Dio _dio;
  final TokenStorageService _tokenStorage = TokenStorageService.instance;

  /// Singleton instance
  static HttpClientService get instance {
    _instance ??= HttpClientService._internal();
    return _instance!;
  }

  HttpClientService._internal() {
    _initializeDio();
  }

  /// Initialize Dio with configuration
  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: Duration(milliseconds: ApiConfig.connectTimeout),
      receiveTimeout: Duration(milliseconds: ApiConfig.receiveTimeout),
      sendTimeout: Duration(milliseconds: ApiConfig.sendTimeout),
      headers: {
        ApiConfig.contentTypeHeader: 'application/json',
        ApiConfig.acceptHeader: 'application/json',
        ApiConfig.userAgentHeader: ApiConfig.userAgent,
      },
    ));

    // Add interceptors
    _dio.interceptors.add(_createAuthInterceptor());
    
    if (ApiConfig.isDebugMode) {
      _dio.interceptors.add(_createLoggingInterceptor());
    }
  }

  /// Create authentication interceptor
  InterceptorsWrapper _createAuthInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token to requests that need it
        final token = await _tokenStorage.getToken();
        if (token != null && _requiresAuth(options.path)) {
          options.headers[ApiConfig.authorizationHeader] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        // Handle token expiration
        if (error.response?.statusCode == ApiConfig.statusUnauthorized) {
          await _tokenStorage.clearToken();
          // You might want to navigate to login screen here
        }
        handler.next(error);
      },
    );
  }

  /// Create logging interceptor for debug mode
  InterceptorsWrapper _createLoggingInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        debugPrint('🚀 REQUEST: ${options.method} ${options.uri}');
        debugPrint('📤 Headers: ${options.headers}');
        if (options.data != null) {
          debugPrint('📤 Data: ${options.data}');
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        debugPrint('✅ RESPONSE: ${response.statusCode} ${response.requestOptions.uri}');
        debugPrint('📥 Data: ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) {
        debugPrint('❌ ERROR: ${error.response?.statusCode} ${error.requestOptions.uri}');
        debugPrint('❌ Message: ${error.message}');
        if (error.response?.data != null) {
          debugPrint('❌ Data: ${error.response?.data}');
        }
        handler.next(error);
      },
    );
  }

  /// Check if endpoint requires authentication
  bool _requiresAuth(String path) {
    final authRequiredPaths = [
      ApiConfig.profileEndpoint,
      ApiConfig.logoutEndpoint,
      ApiConfig.changePasswordEndpoint,
      // Financial Data Endpoints
      ApiConfig.transactionsEndpoint,
      ApiConfig.accountsEndpoint,
      ApiConfig.categoriesEndpoint,
      ApiConfig.financialSummaryEndpoint,
      ApiConfig.dashboardEndpoint,
    ];

    // Debug logging to see what path is being checked
    debugPrint('🔍 Checking auth for path: $path');

    // Check if path contains any of the auth required endpoints
    final requiresAuth = authRequiredPaths.any((authPath) => path.contains(authPath));
    debugPrint('🔐 Path $path requires auth: $requiresAuth');

    return requiresAuth;
  }

  /// Generic GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Handle successful response
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    return ApiResponse.fromJson(
      response.data,
      fromJson,
    );
  }

  /// Handle errors
  ApiResponse<T> _handleError<T>(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return ApiResponse.error(
            message: 'Request timeout. Please try again.',
            statusCode: error.response?.statusCode,
          );
        case DioExceptionType.connectionError:
          return ApiResponse.error(
            message: 'Network error. Please check your internet connection.',
            statusCode: error.response?.statusCode,
          );
        case DioExceptionType.badResponse:
          final responseData = error.response?.data;
          if (responseData is Map<String, dynamic>) {
            return ApiResponse.fromJson(responseData, null);
          }
          return ApiResponse.error(
            message: 'Server error. Please try again later.',
            statusCode: error.response?.statusCode,
          );
        default:
          return ApiResponse.error(
            message: error.message ?? 'Unknown error occurred',
            statusCode: error.response?.statusCode,
          );
      }
    }
    
    return ApiResponse.error(
      message: error.toString(),
    );
  }

  /// Get Dio instance for custom requests
  Dio get dio => _dio;
}
