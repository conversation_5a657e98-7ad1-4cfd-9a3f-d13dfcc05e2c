import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../core/app_export.dart';
import '../../core/services/file_download_service.dart';
import './widgets/bulk_actions_widget.dart';
import './widgets/date_section_header_widget.dart';
import './widgets/empty_state_widget.dart';
import './widgets/filter_bottom_sheet_widget.dart';
import './widgets/filter_chips_widget.dart';
import './widgets/search_bar_widget.dart';
import './widgets/sort_menu_widget.dart';
import './widgets/transaction_card_widget.dart';
import '../../widgets/ads/banner_ad_widget.dart';
import '../../widgets/ads/native_ad_widget.dart';
import '../../core/services/ad_service.dart';
import 'package:sizer/sizer.dart';

class TransactionHistoryScreen extends StatefulWidget {
  const TransactionHistoryScreen({super.key});

  @override
  State<TransactionHistoryScreen> createState() =>
      _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends State<TransactionHistoryScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  String _searchQuery = '';
  Map<String, dynamic> _activeFilters = {};
  String _sortBy = 'date';
  bool _isAscending = false;
  bool _isMultiSelectMode = false;
  Set<int> _selectedTransactions = {};
  bool _isLoading = false;

  late final FinancialDataManager _financialDataManager;
  List<Transaction> _allTransactions = [];
  bool _isInitialized = false;
  List<Transaction> _filteredTransactions = [];
  Map<String, List<Transaction>> _groupedTransactions = {};

  @override
  void initState() {
    super.initState();
    _financialDataManager = FinancialDataManager.instance;
    _scrollController.addListener(_onScroll);
    _initializeData();
  }

  /// Initialize transaction data with performance optimization
  Future<void> _initializeData() async {
    if (!_isInitialized) {
      setState(() {
        _isLoading = true;
      });

      try {
        // First, try to load from cache for immediate display
        _allTransactions = _financialDataManager.transactions;
        if (_allTransactions.isNotEmpty) {
          _filteredTransactions = List.from(_allTransactions);
          _groupTransactionsByDate();
          setState(() {
            _isLoading = false;
            _isInitialized = true;
          });
        }

        // Then load fresh data in background if online
        await _financialDataManager.loadAllTransactions();
        _allTransactions = _financialDataManager.transactions;
        _filteredTransactions = List.from(_allTransactions);
        _groupTransactionsByDate();

        if (mounted) {
          setState(() {
            _isLoading = false;
            _isInitialized = true;
          });
        }
      } catch (e) {
        debugPrint('Error initializing transaction data: $e');
        if (mounted) {
          setState(() {
            _isLoading = false;
            _isInitialized = true;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreTransactions();
    }
  }

  void _loadMoreTransactions() {
    // Removed pagination simulation for better performance
    // All transactions are loaded at once for optimal scrolling
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _filterAndSortTransactions();
    });
  }

  void _onFilterTap() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SizedBox(
        height: 80.h,
        child: FilterBottomSheetWidget(
          currentFilters: _activeFilters,
          onFiltersChanged: (filters) {
            setState(() {
              _activeFilters = filters;
              _filterAndSortTransactions();
            });
          },
        ),
      ),
    );
  }

  void _onSortChanged(String sortBy, bool ascending) {
    setState(() {
      _sortBy = sortBy;
      _isAscending = ascending;
      _filterAndSortTransactions();
    });
  }

  void _onRemoveFilter(String key, dynamic value) {
    setState(() {
      _activeFilters.remove(key);
      _filterAndSortTransactions();
    });
  }

  void _filterAndSortTransactions() {
    List<Transaction> filtered = List.from(_allTransactions);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((transaction) {
        final description = transaction.displayDescription.toLowerCase();
        final category = transaction.category.toLowerCase();
        final amount = transaction.formattedAmount.toLowerCase();
        final query = _searchQuery.toLowerCase();

        return description.contains(query) ||
            category.contains(query) ||
            amount.contains(query);
      }).toList();
    }

    // Apply filters
    if (_activeFilters['startDate'] != null) {
      final startDate = _activeFilters['startDate'] as DateTime;
      filtered = filtered.where((transaction) {
        return transaction.date
            .isAfter(startDate.subtract(const Duration(days: 1)));
      }).toList();
    }

    if (_activeFilters['endDate'] != null) {
      final endDate = _activeFilters['endDate'] as DateTime;
      filtered = filtered.where((transaction) {
        return transaction.date.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    if (_activeFilters['categories'] != null) {
      final categories = _activeFilters['categories'] as List<String>;
      if (categories.isNotEmpty) {
        filtered = filtered.where((transaction) {
          return categories.contains(transaction.category);
        }).toList();
      }
    }

    if (_activeFilters['accountTypes'] != null) {
      final accountTypes = _activeFilters['accountTypes'] as List<String>;
      if (accountTypes.isNotEmpty) {
        filtered = filtered.where((transaction) {
          return accountTypes.contains(transaction.account);
        }).toList();
      }
    }

    if (_activeFilters['transactionType'] != null) {
      final type = (_activeFilters['transactionType'] as String).toLowerCase();
      filtered = filtered.where((transaction) {
        return transaction.type.toLowerCase() == type;
      }).toList();
    }

    if (_activeFilters['minAmount'] != null ||
        _activeFilters['maxAmount'] != null) {
      final minAmount = (_activeFilters['minAmount'] as double?) ?? 0.0;
      final maxAmount =
          (_activeFilters['maxAmount'] as double?) ?? double.infinity;

      filtered = filtered.where((transaction) {
        return transaction.amount >= minAmount && transaction.amount <= maxAmount;
      }).toList();
    }

    // Apply sorting
    filtered.sort((a, b) {
      switch (_sortBy) {
        case 'date':
          return _isAscending ? a.date.compareTo(b.date) : b.date.compareTo(a.date);
        case 'amount':
          return _isAscending
              ? a.amount.compareTo(b.amount)
              : b.amount.compareTo(a.amount);
        case 'category':
          return _isAscending
              ? a.category.compareTo(b.category)
              : b.category.compareTo(a.category);
        default:
          return 0;
      }
    });

    _filteredTransactions = filtered;
    _groupTransactionsByDate();
  }

  void _groupTransactionsByDate() {
    _groupedTransactions.clear();

    for (final transaction in _filteredTransactions) {
      final date = transaction.date;
      // Use ISO format for key to ensure it can be parsed back
      final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      if (!_groupedTransactions.containsKey(dateKey)) {
        _groupedTransactions[dateKey] = [];
      }
      _groupedTransactions[dateKey]!.add(transaction);
    }
  }

  void _onTransactionLongPress(int? transactionId) {
    if (transactionId == null) return;
    setState(() {
      _isMultiSelectMode = true;
      _selectedTransactions.add(transactionId);
    });
  }

  void _onTransactionTap(int? transactionId) {
    if (transactionId == null) return;
    if (_isMultiSelectMode) {
      setState(() {
        if (_selectedTransactions.contains(transactionId)) {
          _selectedTransactions.remove(transactionId);
          if (_selectedTransactions.isEmpty) {
            _isMultiSelectMode = false;
          }
        } else {
          _selectedTransactions.add(transactionId);
        }
      });
    }
  }

  void _cancelMultiSelect() {
    setState(() {
      _isMultiSelectMode = false;
      _selectedTransactions.clear();
    });
  }

  void _deleteSelectedTransactions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Transactions'),
        content: Text(
            'Are you sure you want to delete ${_selectedTransactions.length} selected transactions? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child:
                Text('Cancel', style: TextStyle(color: AppTheme.neutralGray)),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _allTransactions.removeWhere((transaction) =>
                    _selectedTransactions.contains(transaction.id));
                _filterAndSortTransactions();
                _cancelMultiSelect();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(
                        '${_selectedTransactions.length} transactions deleted')),
              );
            },
            child: Text('Delete', style: TextStyle(color: AppTheme.alertRed)),
          ),
        ],
      ),
    );
  }

  void _exportSelectedTransactions() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
          content: Text(
              'Exporting ${_selectedTransactions.length} transactions...')),
    );
    _cancelMultiSelect();
  }

  void _categorizeSelectedTransactions() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
          content: Text(
              'Categorizing ${_selectedTransactions.length} transactions...')),
    );
    _cancelMultiSelect();
  }

  Future<void> _onRefresh() async {
    setState(() {
      _isLoading = true;
    });

    // Refresh transactions from API
    await _financialDataManager.loadAllTransactions();
    _allTransactions = _financialDataManager.transactions;
    _filterAndSortTransactions();

    setState(() {
      _isLoading = false;
    });
  }

  int _getActiveFilterCount() {
    int count = 0;
    if (_activeFilters['startDate'] != null ||
        _activeFilters['endDate'] != null) count++;
    if (_activeFilters['categories'] != null &&
        (_activeFilters['categories'] as List).isNotEmpty) count++;
    if (_activeFilters['accountTypes'] != null &&
        (_activeFilters['accountTypes'] as List).isNotEmpty) count++;
    if (_activeFilters['transactionType'] != null) count++;
    if (_activeFilters['minAmount'] != null ||
        _activeFilters['maxAmount'] != null) count++;
    return count;
  }

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return ListenableBuilder(
      listenable: _financialDataManager,
      builder: (context, child) {
        return Scaffold(
      backgroundColor:
          isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      appBar: AppBar(
        title: Text(
          'Transaction History',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        actions: [
          if (!_isMultiSelectMode) ...[
            SortMenuWidget(
              currentSortBy: _sortBy,
              isAscending: _isAscending,
              onSortChanged: _onSortChanged,
            ),
          ],
          SizedBox(width: 2.w),
        ],
        leading: _isMultiSelectMode
            ? IconButton(
                onPressed: _cancelMultiSelect,
                icon: CustomIconWidget(
                  iconName: 'close',
                  color: isDark
                      ? AppTheme.textHighEmphasisDark
                      : AppTheme.textHighEmphasisLight,
                  size: 6.w,
                ),
              )
            : null,
      ),
      body: Column(
        children: [
          // Search bar
          if (!_isMultiSelectMode)
            SearchBarWidget(
              searchQuery: _searchQuery,
              onSearchChanged: _onSearchChanged,
              onFilterTap: _onFilterTap,
              activeFilterCount: _getActiveFilterCount(),
            ),

          // Filter chips
          if (!_isMultiSelectMode && _activeFilters.isNotEmpty)
            FilterChipsWidget(
              activeFilters: _activeFilters,
              onRemoveFilter: _onRemoveFilter,
            ),

          // Transaction list
          Expanded(
            child: _filteredTransactions.isEmpty
                ? _buildEmptyState()
                : RefreshIndicator(
                    onRefresh: _onRefresh,
                    color: AppTheme.primaryTeal,
                    child: ListView.builder(
                      controller: _scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemCount: _getListItemCount(),
                      itemBuilder: (context, index) => _buildListItem(index),
                    ),
                  ),
          ),
        ],
      ),
      bottomSheet: _isMultiSelectMode
          ? BulkActionsWidget(
              selectedCount: _selectedTransactions.length,
              onDelete: _deleteSelectedTransactions,
              onExport: _exportSelectedTransactions,
              onCategorize: _categorizeSelectedTransactions,
              onCancel: _cancelMultiSelect,
            )
          : null,
      floatingActionButton: !_isMultiSelectMode
          ? FloatingActionButton(
              onPressed: () =>
                  Navigator.pushNamed(context, '/add-transaction-screen'),
              child: CustomIconWidget(
                iconName: 'add',
                color: Colors.white,
                size: 6.w,
              ),
            )
          : null,
      bottomNavigationBar: const BannerAdWidget(),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    if (_searchQuery.isNotEmpty) {
      return EmptyStateWidget(
        type: 'no_search_results',
      );
    } else if (_activeFilters.isNotEmpty) {
      return EmptyStateWidget(
        type: 'no_filtered_results',
        onAction: () {
          setState(() {
            _activeFilters.clear();
            _filterAndSortTransactions();
          });
        },
      );
    } else {
      return EmptyStateWidget(
        type: 'no_transactions',
        onAction: () => Navigator.pushNamed(context, '/add-transaction-screen'),
      );
    }
  }

  int _getListItemCount() {
    int count = 0;
    int transactionCount = 0;
    for (final entry in _groupedTransactions.entries) {
      count++; // Date header
      for (int i = 0; i < entry.value.length; i++) {
        count++;
        transactionCount++;
        if (transactionCount % 6 == 0) {
          count++; // Ad
        }
      }
    }
    if (_isLoading) count++; // Loading indicator
    return count;
  }

  Widget _buildListItem(int index) {
    int currentIndex = 0;
    int transactionCount = 0;

    for (final entry in _groupedTransactions.entries) {
      if (currentIndex == index) {
        // Date section header
        final date = DateTime.parse(entry.key);
        final totalAmount = entry.value.fold<double>(0.0, (sum, transaction) {
          final amount = transaction.amount;
          final isIncome = transaction.type.toLowerCase() == 'income';
          return sum + (isIncome ? amount : -amount);
        });

        return DateSectionHeaderWidget(
          date: date,
          totalAmount: totalAmount,
          transactionCount: entry.value.length,
        );
      }
      currentIndex++;

      for (final transaction in entry.value) {
        if (currentIndex == index) {
          // Transaction card
          final transactionId = transaction.id;
          return TransactionCardWidget(
            transaction: transaction,
            isSelected: _selectedTransactions.contains(transactionId),
            onLongPress: () => _onTransactionLongPress(transactionId),
            onTap: () => _onTransactionTap(transactionId),
            onEdit: () {
              _editTransaction(transaction);
            },
            onDelete: () async {
              if (transactionId != null) {
                final success =
                    await _financialDataManager.deleteTransaction(transactionId);
                if (success) {
                  _filterAndSortTransactions();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Transaction deleted')),
                    );
                  }
                }
              }
            },
          );
        }
        currentIndex++;
        transactionCount++;

        if (transactionCount % 6 == 0) {
          if (currentIndex == index) {
            return const Padding(
              padding: EdgeInsets.symmetric(vertical: 8.0),
              child: NativeAdWidget(),
            );
          }
          currentIndex++;
        }
      }
    }

    // Loading indicator
    if (_isLoading) {
      return Container(
        padding: EdgeInsets.all(4.w),
        child: Center(
          child: CircularProgressIndicator(
            color: AppTheme.primaryTeal,
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }



  /// Edit transaction - navigate to edit screen with transaction data
  void _editTransaction(Transaction transaction) {
    Navigator.pushNamed(
      context,
      '/add-transaction-screen',
      arguments: {
        'id': transaction.id,
        'type': transaction.type,
        'amount': transaction.amount,
        'category': transaction.category,
        'account': transaction.account,
        'description': transaction.description,
        'notes': transaction.notes,
        'date': transaction.date.toIso8601String(),
        'isEdit': true,
      },
    );
  }
}
