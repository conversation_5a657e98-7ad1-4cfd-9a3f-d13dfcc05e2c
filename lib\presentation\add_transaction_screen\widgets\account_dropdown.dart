import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class AccountDropdown extends StatefulWidget {
  final Account? selectedAccount;
  final ValueChanged<Account?> onAccountSelected;

  const AccountDropdown({
    Key? key,
    this.selectedAccount,
    required this.onAccountSelected,
  }) : super(key: key);

  @override
  State<AccountDropdown> createState() => _AccountDropdownState();
}

class _AccountDropdownState extends State<AccountDropdown> {
  late final FinancialDataManager _financialDataManager;
  List<Account> _accounts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _financialDataManager = FinancialDataManager.instance;
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
    });

    // Get accounts from financial data manager
    _accounts = _financialDataManager.activeAccounts;

    // If no accounts loaded yet, try to load them
    if (_accounts.isEmpty) {
      await _financialDataManager.loadAccounts();
      _accounts = _financialDataManager.activeAccounts;
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: AppTheme.cardSurface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.borderSubtle),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.h),
          GestureDetector(
            onTap: () => _showAccountSelector(context),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: AppTheme.lightTheme.colorScheme.surface,
                border: Border.all(color: AppTheme.borderSubtle),
              ),
              child: Row(
                children: [
                  if (widget.selectedAccount != null) ...[
                    _getAccountIcon(widget.selectedAccount!),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.selectedAccount!.displayName,
                            style: AppTheme.lightTheme.textTheme.titleMedium
                                ?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'Balance: ${widget.selectedAccount!.formattedBalance}',
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: AppTheme.textMediumEmphasisLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    CustomIconWidget(
                      iconName: 'account_balance_wallet',
                      color: AppTheme.textDisabledLight,
                      size: 24,
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Select Account (Optional)',
                            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                              color: AppTheme.textDisabledLight,
                            ),
                          ),
                          Text(
                            'Will use "General" account if none selected',
                            style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                              color: AppTheme.textLowEmphasisLight,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  CustomIconWidget(
                    iconName: 'keyboard_arrow_down',
                    color: AppTheme.textMediumEmphasisLight,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAccountSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 1.h),
              width: 12.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: AppTheme.borderSubtle,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Text(
                'Select Account',
                style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // None option
            ListTile(
              leading: Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.neutralGray.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: CustomIconWidget(
                  iconName: 'account_balance_wallet',
                  color: AppTheme.neutralGray,
                  size: 24,
                ),
              ),
              title: Text(
                'None (Use General Account)',
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: widget.selectedAccount == null ? AppTheme.primaryTeal : null,
                ),
              ),
              subtitle: Text(
                'Will create a default "General" account',
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.textMediumEmphasisLight,
                ),
              ),
              trailing: widget.selectedAccount == null
                  ? CustomIconWidget(
                      iconName: 'check_circle',
                      color: AppTheme.primaryTeal,
                      size: 24,
                    )
                  : null,
              onTap: () {
                widget.onAccountSelected(null);
                Navigator.pop(context);
              },
            ),

            if (_accounts.isNotEmpty) ...[
              Divider(),

              ListView.builder(
                shrinkWrap: true,
                itemCount: _accounts.length,
                itemBuilder: (context, index) {
                  final account = _accounts[index];
                  final isSelected = widget.selectedAccount?.id == account.id;

                return ListTile(
                  leading: _getAccountIcon(account),
                  title: Text(
                    account.displayName,
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppTheme.primaryTeal : null,
                    ),
                  ),
                  subtitle: Text(
                    'Balance: ${account.formattedBalance}',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.textMediumEmphasisLight,
                    ),
                  ),
                  trailing: isSelected
                      ? CustomIconWidget(
                          iconName: 'check_circle',
                          color: AppTheme.primaryTeal,
                          size: 24,
                        )
                      : null,
                  onTap: () {
                    widget.onAccountSelected(account);
                    Navigator.pop(context);
                  },
                );
              },
            ),
            ],
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  Widget _getAccountIcon(Account account) {
    final iconName = account.accountIcon;
    final colorHex = account.accountColor;
    final iconColor = Color(int.parse(colorHex.replaceFirst('#', '0xFF')));

    return Container(
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: CustomIconWidget(
        iconName: iconName,
        color: iconColor,
        size: 24,
      ),
    );
  }
}
