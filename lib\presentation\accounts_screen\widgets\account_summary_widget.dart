import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../../core/app_export.dart';

class AccountSummaryWidget extends StatelessWidget {
  final double totalBalance;
  final int accountCount;

  const AccountSummaryWidget({
    Key? key,
    required this.totalBalance,
    required this.accountCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ListenableBuilder(
      listenable: FormattingService.instance,
      builder: (context, child) {
        final formattedBalance = FormattingService.instance.formatCurrency(totalBalance);

    return Container(
      margin: EdgeInsets.all(4.w),
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryTeal,
            AppTheme.primaryTeal.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryTeal.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Balance',
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$accountCount ${accountCount == 1 ? 'Account' : 'Accounts'}',
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 2.h),
          
          // Balance Amount
          Text(
            formattedBalance,
            style: AppTheme.lightTheme.textTheme.headlineLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 32.sp,
            ),
          ),
          
          SizedBox(height: 1.h),
          
          // Balance Status
          Row(
            children: [
              CustomIconWidget(
                iconName: totalBalance >= 0 ? 'trending_up' : 'trending_down',
                color: Colors.white.withValues(alpha: 0.9),
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                totalBalance >= 0 ? 'Positive Balance' : 'Negative Balance',
                style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 3.h),
          
          // Quick Stats
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: 'account_balance_wallet',
                  label: 'Assets',
                  value: _getAssetsTotal(),
                ),
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: _buildStatItem(
                  icon: 'credit_card',
                  label: 'Liabilities',
                  value: _getLiabilitiesTotal(),
                ),
              ),
            ],
          ),
        ],
      ),
        );
      },
    );
  }

  Widget _buildStatItem({
    required String icon,
    required String label,
    required double value,
  }) {
    final formattedValue = FormattingService.instance.formatCurrency(value.abs());

    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: icon,
                color: Colors.white.withValues(alpha: 0.8),
                size: 18,
              ),
              SizedBox(width: 2.w),
              Text(
                label,
                style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          Text(
            formattedValue,
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }

  double _getAssetsTotal() {
    // This would be calculated from actual account data
    // For now, return positive balance or 0
    return totalBalance > 0 ? totalBalance : 0;
  }

  double _getLiabilitiesTotal() {
    // This would be calculated from actual account data
    // For now, return negative balance or 0
    return totalBalance < 0 ? totalBalance.abs() : 0;
  }
}
