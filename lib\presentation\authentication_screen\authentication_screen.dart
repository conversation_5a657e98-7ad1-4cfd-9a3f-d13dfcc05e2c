import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../core/services/auth_state_manager.dart';
import './widgets/app_logo_widget.dart';
import './widgets/guest_mode_widget.dart';
import './widgets/login_form_widget.dart';
import './widgets/signup_link_widget.dart';
import './widgets/register_form_widget.dart';

class AuthenticationScreen extends StatefulWidget {
  const AuthenticationScreen({Key? key}) : super(key: key);

  @override
  State<AuthenticationScreen> createState() => _AuthenticationScreenState();
}

class _AuthenticationScreenState extends State<AuthenticationScreen> {
  bool _isLoading = false;
  bool _isRegisterMode = false;
  final ScrollController _scrollController = ScrollController();
  late final AuthStateManager _authStateManager;

  @override
  void initState() {
    super.initState();
    _authStateManager = AuthStateManager.instance;
    // Initialize auth state if not already done
    _authStateManager.initializeAuthState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin(String email, String password) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _authStateManager.login(
        email: email,
        password: password,
      );

      if (success) {
        // Success haptic feedback
        HapticFeedback.lightImpact();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'check_circle',
                    color: Colors.white,
                    size: 5.w,
                  ),
                  SizedBox(width: 2.w),
                  const Text('Login successful!'),
                ],
              ),
              backgroundColor: AppTheme.successGreen,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Navigate to dashboard
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/dashboard-screen');
        }
      } else {
        // Show error from auth state manager
        if (mounted) {
          final errorMessage = _authStateManager.error ?? 'Login failed';
          _showErrorDialog('Login Failed', errorMessage);
        }
      }
    } catch (e) {
      // Network or server error
      if (mounted) {
        _showErrorDialog(
          'Connection Error',
          'Unable to connect to the server. Please check your internet connection and try again.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleRegister({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _authStateManager.register(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
      );

      if (success) {
        // Success haptic feedback
        HapticFeedback.lightImpact();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'check_circle',
                    color: Colors.white,
                    size: 5.w,
                  ),
                  SizedBox(width: 2.w),
                  const Text('Registration successful!'),
                ],
              ),
              backgroundColor: AppTheme.successGreen,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Navigate to dashboard
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/dashboard-screen');
        }
      } else {
        // Show error from auth state manager
        if (mounted) {
          final errorMessage = _authStateManager.error ?? 'Registration failed';
          _showErrorDialog('Registration Failed', errorMessage);
        }
      }
    } catch (e) {
      // Network or server error
      if (mounted) {
        _showErrorDialog(
          'Connection Error',
          'Unable to connect to the server. Please check your internet connection and try again.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleGuestLogin() {
    setState(() {
      _isLoading = true;
    });

    // Simulate brief loading
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        HapticFeedback.lightImpact();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                CustomIconWidget(
                  iconName: 'person_outline',
                  color: Colors.white,
                  size: 5.w,
                ),
                SizedBox(width: 2.w),
                const Text('Welcome, Guest!'),
              ],
            ),
            backgroundColor: AppTheme.primaryTeal,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Navigate to dashboard in guest mode
        Navigator.pushReplacementNamed(context, '/dashboard-screen');
      }
    });
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              CustomIconWidget(
                iconName: 'error_outline',
                color: AppTheme.alertRed,
                size: 6.w,
              ),
              SizedBox(width: 2.w),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: AppTheme.alertRed,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          content: Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'OK',
                style: TextStyle(
                  color: AppTheme.primaryTeal,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: EdgeInsets.symmetric(horizontal: 6.w),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
              ),
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    SizedBox(height: 8.h),

                    // App Logo Section
                    const AppLogoWidget(),

                    SizedBox(height: 6.h),

                    // Form Section (Login or Register)
                    if (!_isRegisterMode) ...[
                      LoginFormWidget(
                        onLogin: _handleLogin,
                        isLoading: _isLoading,
                      ),
                      SizedBox(height: 4.h),
                      // Guest Mode Section
                      GuestModeWidget(
                        onGuestLogin: _handleGuestLogin,
                        isLoading: _isLoading,
                      ),
                    ] else ...[
                      RegisterFormWidget(
                        onRegister: _handleRegister,
                        isLoading: _isLoading,
                      ),
                    ],

                    const Spacer(),

                    // Sign Up/Login Link Section
                    SignupLinkWidget(
                      isLoading: _isLoading,
                      isRegisterMode: _isRegisterMode,
                      onToggleMode: () {
                        setState(() {
                          _isRegisterMode = !_isRegisterMode;
                        });
                      },
                    ),

                    SizedBox(height: 2.h),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
