import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../core/services/auth_service.dart';
import '../authentication_screen/widgets/app_logo_widget.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({Key? key}) : super(key: key);

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _tokenController = TextEditingController();
  final AuthService _authService = AuthService.instance;

  bool _isLoading = false;
  bool _isFormValid = false;
  bool _emailSent = false;  // Track if email has been sent
  bool _isTokenValid = false;  // Track if token input is valid
  String _sentEmail = '';  // Store the email that was sent

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_validateForm);
    _tokenController.addListener(_validateTokenForm);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _tokenController.dispose();
    super.dispose();
  }

  void _validateForm() {
    final isValid = _emailController.text.isNotEmpty &&
        _authService.isValidEmail(_emailController.text);

    if (_isFormValid != isValid) {
      setState(() {
        _isFormValid = isValid;
      });
    }
  }

  void _validateTokenForm() {
    final isValid = _tokenController.text.isNotEmpty &&
        _tokenController.text.length >= 10; // Basic token validation

    if (_isTokenValid != isValid) {
      setState(() {
        _isTokenValid = isValid;
      });
    }
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!_authService.isValidEmail(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  Future<void> _handleForgotPassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _authService.forgotPassword(
        email: _emailController.text.trim(),
      );

      if (mounted) {
        if (response.success) {
          setState(() {
            _emailSent = true;
            _sentEmail = _emailController.text.trim();
          });
          print('✅ Email sent successfully, _emailSent = $_emailSent, _sentEmail = $_sentEmail');
          _showSuccessDialog();
        } else {
          _showErrorDialog(
            'Reset Failed',
            response.message,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog(
          'Connection Error',
          'Unable to connect to the server. Please check your internet connection and try again.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleResetWithToken() async {
    if (!_isTokenValid || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Navigate to reset password screen with token and email
      Navigator.of(context).pushNamed(
        '/reset-password-screen',
        arguments: {
          'token': _tokenController.text.trim(),
          'email': _sentEmail,
        },
      );
    } catch (e) {
      _showErrorDialog('Error', 'Failed to proceed with password reset.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _goBackToEmailStep() {
    setState(() {
      _emailSent = false;
      _tokenController.clear();
      _isTokenValid = false;
    });
  }

  String? _validateToken(String? value) {
    if (value == null || value.isEmpty) {
      return 'Reset token is required';
    }
    if (value.length < 10) {
      return 'Please enter a valid reset token';
    }
    return null;
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: AppTheme.successGreen,
                size: 6.w,
              ),
              SizedBox(width: 2.w),
              Text(
                'Email Sent',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppTheme.successGreen,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'We\'ve sent a password reset link to:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: 1.h),
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.primaryTeal.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _emailController.text.trim(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryTeal,
                  ),
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                'Check your email for the reset token, then enter it below to continue.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.neutralGray,
                ),
              ),
              SizedBox(height: 2.h),
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: AppTheme.primaryPurple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppTheme.primaryPurple.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppTheme.primaryPurple,
                      size: 4.w,
                    ),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: Text(
                        'Note: You will only receive the email if you have a registered FinTrack account. If you don\'t have an account, please register first.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.primaryPurple,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            SizedBox(
              width: 25.w,
              height: 5.h,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Only close the dialog
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryTeal,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Continue',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.error,
                color: AppTheme.errorRed,
                size: 6.w,
              ),
              SizedBox(width: 2.w),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppTheme.errorRed,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'OK',
                style: TextStyle(
                  color: AppTheme.primaryTeal,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStepIndicators() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 3.h),
      decoration: BoxDecoration(
        color: AppTheme.primaryTeal.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryTeal.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Step 1
          Row(
            children: [
              Container(
                width: 6.w,
                height: 6.w,
                decoration: BoxDecoration(
                  color: AppTheme.primaryTeal,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 3.w,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  'Step 1: Enter your email and tap "Send Reset Link"',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.primaryTeal,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Step 2
          Row(
            children: [
              Container(
                width: 6.w,
                height: 6.w,
                decoration: BoxDecoration(
                  color: _emailSent ? AppTheme.primaryTeal : AppTheme.neutralGray.withOpacity(0.3),
                  shape: BoxShape.circle,
                ),
                child: _emailSent
                    ? Icon(
                        Icons.email,
                        color: Colors.white,
                        size: 3.w,
                      )
                    : Text(
                        '2',
                        style: TextStyle(
                          color: AppTheme.neutralGray,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  'Step 2: Check your email for the reset token',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _emailSent ? AppTheme.primaryTeal : AppTheme.neutralGray,
                    fontWeight: _emailSent ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Step 3
          Row(
            children: [
              Container(
                width: 6.w,
                height: 6.w,
                decoration: BoxDecoration(
                  color: _emailSent ? AppTheme.primaryTeal : AppTheme.neutralGray.withOpacity(0.3),
                  shape: BoxShape.circle,
                ),
                child: _emailSent
                    ? Icon(
                        Icons.lock_reset,
                        color: Colors.white,
                        size: 3.w,
                      )
                    : Text(
                        '3',
                        style: TextStyle(
                          color: AppTheme.neutralGray,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  'Step 3: Enter the token below to reset your password',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _emailSent ? AppTheme.primaryTeal : AppTheme.neutralGray,
                    fontWeight: _emailSent ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print('🔄 Building forgot password screen: _emailSent = $_emailSent');
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Theme.of(context).iconTheme.color,
            size: 5.w,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Theme.of(context).brightness == Brightness.dark
              ? Brightness.light
              : Brightness.dark,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: 4.h),

              // App Logo
              const AppLogoWidget(),

              SizedBox(height: 6.h),

              // Dynamic Title
              Text(
                _emailSent ? 'Enter Reset Token' : 'Forgot Password?',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryTeal,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 2.h),

              // Dynamic description based on step
              if (!_emailSent)
                Text(
                  'Don\'t worry! Enter your email address and we\'ll send you a link to reset your password.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.neutralGray,
                  ),
                  textAlign: TextAlign.center,
                )
              else
                Column(
                  children: [
                    Text(
                      'Reset link sent to $_sentEmail',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.successGreen,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      'Check your email for the reset token and enter it below to continue.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.neutralGray,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),

              SizedBox(height: 4.h),

              // Step Indicators
              _buildStepIndicators(),

              SizedBox(height: 6.h),

              // Email Form (Step 1)
              if (!_emailSent) ...[
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Email Field
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.done,
                        enabled: !_isLoading,
                        decoration: InputDecoration(
                          labelText: 'Email Address',
                          hintText: 'Enter your email address',
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(3.w),
                            child: CustomIconWidget(
                              iconName: 'email',
                              color: AppTheme.neutralGray,
                              size: 5.w,
                            ),
                          ),
                        ),
                        validator: _validateEmail,
                        style: Theme.of(context).textTheme.bodyLarge,
                        onFieldSubmitted: (_) {
                          if (_isFormValid && !_isLoading) {
                            _handleForgotPassword();
                          }
                        },
                      ),

                      SizedBox(height: 4.h),

                      // Send Reset Link Button
                      SizedBox(
                        width: double.infinity,
                        height: 6.h,
                        child: ElevatedButton(
                          onPressed: _isFormValid && !_isLoading ? _handleForgotPassword : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryTeal,
                            foregroundColor: Colors.white,
                            disabledBackgroundColor: AppTheme.neutralGray.withOpacity(0.3),
                            disabledForegroundColor: AppTheme.neutralGray,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: _isLoading
                              ? SizedBox(
                                  width: 5.w,
                                  height: 5.w,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : Text(
                                  'Send Reset Link',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Token Input Form (Step 2)
              if (_emailSent) ...[
                Form(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Token Input Field
                      TextFormField(
                        controller: _tokenController,
                        keyboardType: TextInputType.text,
                        textInputAction: TextInputAction.done,
                        enabled: !_isLoading,
                        decoration: InputDecoration(
                          labelText: 'Reset Token',
                          hintText: 'Enter the token from your email',
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(3.w),
                            child: Icon(
                              Icons.security,
                              color: AppTheme.neutralGray,
                              size: 5.w,
                            ),
                          ),
                          helperText: 'Check your email for the reset token',
                          helperStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.neutralGray,
                          ),
                        ),
                        validator: _validateToken,
                        style: Theme.of(context).textTheme.bodyLarge,
                        onFieldSubmitted: (_) {
                          if (_isTokenValid && !_isLoading) {
                            _handleResetWithToken();
                          }
                        },
                      ),

                      SizedBox(height: 4.h),

                      // Reset Password Button
                      SizedBox(
                        width: double.infinity,
                        height: 6.h,
                        child: ElevatedButton(
                          onPressed: _isTokenValid && !_isLoading ? _handleResetWithToken : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryTeal,
                            foregroundColor: Colors.white,
                            disabledBackgroundColor: AppTheme.neutralGray.withOpacity(0.3),
                            disabledForegroundColor: AppTheme.neutralGray,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: _isLoading
                              ? SizedBox(
                                  width: 5.w,
                                  height: 5.w,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : Text(
                                  'Reset Password',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                        ),
                      ),

                      SizedBox(height: 3.h),

                      // Back to Email Step Button
                      TextButton.icon(
                        onPressed: _isLoading ? null : _goBackToEmailStep,
                        icon: Icon(
                          Icons.arrow_back,
                          color: AppTheme.primaryTeal,
                          size: 4.w,
                        ),
                        label: Text(
                          'Back to Email Step',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppTheme.primaryTeal,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              SizedBox(height: 4.h),

              // Back to Login Link
              TextButton(
                onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    text: 'Remember your password? ',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.neutralGray,
                    ),
                    children: [
                      TextSpan(
                        text: 'Back to Login',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.primaryTeal,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
