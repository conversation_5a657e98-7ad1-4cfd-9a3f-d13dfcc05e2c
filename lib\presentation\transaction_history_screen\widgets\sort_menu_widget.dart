import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class SortMenuWidget extends StatelessWidget {
  final String currentSortBy;
  final bool isAscending;
  final Function(String, bool) onSortChanged;

  const SortMenuWidget({
    super.key,
    required this.currentSortBy,
    required this.isAscending,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<Map<String, dynamic>>(
      icon: CustomIconWidget(
        iconName: 'sort',
        color: Theme.of(context).brightness == Brightness.dark
            ? AppTheme.textMediumEmphasisDark
            : AppTheme.textMediumEmphasisLight,
        size: 6.w,
      ),
      onSelected: (Map<String, dynamic> value) {
        onSortChanged(value['sortBy'] as String, value['ascending'] as bool);
      },
      itemBuilder: (BuildContext context) => [
        _buildPopupMenuItem(
          context,
          'Date (Newest First)',
          'date',
          false,
          'calendar_today',
        ),
        _buildPopupMenuItem(
          context,
          'Date (Oldest First)',
          'date',
          true,
          'calendar_today',
        ),
        _buildPopupMenuItem(
          context,
          'Amount (High to Low)',
          'amount',
          false,
          'trending_down',
        ),
        _buildPopupMenuItem(
          context,
          'Amount (Low to High)',
          'amount',
          true,
          'trending_up',
        ),
        _buildPopupMenuItem(
          context,
          'Category (A-Z)',
          'category',
          true,
          'category',
        ),
        _buildPopupMenuItem(
          context,
          'Category (Z-A)',
          'category',
          false,
          'category',
        ),
      ],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 8,
      color: Theme.of(context).brightness == Brightness.dark
          ? AppTheme.cardSurfaceDark
          : AppTheme.cardSurface,
    );
  }

  PopupMenuItem<Map<String, dynamic>> _buildPopupMenuItem(
    BuildContext context,
    String title,
    String sortBy,
    bool ascending,
    String iconName,
  ) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;
    final bool isSelected = currentSortBy == sortBy && isAscending == ascending;

    return PopupMenuItem<Map<String, dynamic>>(
      value: {'sortBy': sortBy, 'ascending': ascending},
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.h),
        child: Row(
          children: [
            CustomIconWidget(
              iconName: iconName,
              color: isSelected
                  ? AppTheme.primaryTeal
                  : (isDark
                      ? AppTheme.textMediumEmphasisDark
                      : AppTheme.textMediumEmphasisLight),
              size: 5.w,
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isSelected
                          ? AppTheme.primaryTeal
                          : (isDark
                              ? AppTheme.textHighEmphasisDark
                              : AppTheme.textHighEmphasisLight),
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
              ),
            ),
            if (isSelected)
              CustomIconWidget(
                iconName: 'check',
                color: AppTheme.primaryTeal,
                size: 4.w,
              ),
          ],
        ),
      ),
    );
  }
}
