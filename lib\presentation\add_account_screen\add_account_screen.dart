import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/account_type_selector.dart';
import './widgets/account_icon_selector.dart';
import './widgets/account_color_selector.dart';

class AddAccountScreen extends StatefulWidget {
  final Account? account; // For editing existing account

  const AddAccountScreen({Key? key, this.account}) : super(key: key);

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _balanceController = TextEditingController();
  final _bankNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  
  late final FinancialDataManager _financialDataManager;
  
  String _selectedAccountType = 'checking';
  String _selectedIcon = 'account_balance_wallet';
  String _selectedColor = '#2196F3';
  bool _isLoading = false;
  bool _isEditMode = false;

  // Account type options
  final List<Map<String, String>> _accountTypes = [
    {'value': 'checking', 'label': 'Checking Account', 'icon': 'account_balance'},
    {'value': 'savings', 'label': 'Savings Account', 'icon': 'savings'},
    {'value': 'credit', 'label': 'Credit Card', 'icon': 'credit_card'},
    {'value': 'cash', 'label': 'Cash', 'icon': 'payments'},
    {'value': 'investment', 'label': 'Investment', 'icon': 'trending_up'},
    {'value': 'loan', 'label': 'Loan', 'icon': 'account_balance_wallet'},
    {'value': 'other', 'label': 'Other', 'icon': 'account_circle'},
  ];

  // Icon options
  final List<String> _iconOptions = [
    'account_balance_wallet',
    'account_balance',
    'savings',
    'credit_card',
    'payments',
    'trending_up',
    'account_circle',
    'business',
    'home',
    'local_atm',
    'monetization_on',
    'attach_money',
  ];

  // Color options
  final List<String> _colorOptions = [
    '#2196F3', // Blue
    '#4CAF50', // Green
    '#FF9800', // Orange
    '#9C27B0', // Purple
    '#F44336', // Red
    '#00BCD4', // Cyan
    '#795548', // Brown
    '#607D8B', // Blue Grey
    '#E91E63', // Pink
    '#3F51B5', // Indigo
    '#009688', // Teal
    '#FFC107', // Amber
  ];

  @override
  void initState() {
    super.initState();
    _financialDataManager = FinancialDataManager.instance;
    _isEditMode = widget.account != null;
    
    if (_isEditMode) {
      _populateFieldsForEdit();
    }
  }

  void _populateFieldsForEdit() {
    final account = widget.account!;
    _nameController.text = account.name;
    _balanceController.text = account.balance.toString();
    _bankNameController.text = account.bankName ?? '';
    _accountNumberController.text = account.accountNumber ?? '';
    _selectedAccountType = account.type;
    _selectedIcon = account.accountIcon;
    _selectedColor = account.accountColor;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _balanceController.dispose();
    _bankNameController.dispose();
    _accountNumberController.dispose();
    super.dispose();
  }

  Future<void> _saveAccount() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final name = _nameController.text.trim();
      final balance = double.tryParse(_balanceController.text.trim()) ?? 0.0;
      final bankName = _bankNameController.text.trim().isNotEmpty 
          ? _bankNameController.text.trim() 
          : null;
      final accountNumber = _accountNumberController.text.trim().isNotEmpty 
          ? _accountNumberController.text.trim() 
          : null;

      bool success;
      if (_isEditMode) {
        success = await _financialDataManager.updateAccount(
          accountId: widget.account!.id!,
          name: name,
          accountType: _selectedAccountType,
          balance: balance,
          bankName: bankName,
          accountNumber: accountNumber,
          accountIcon: _selectedIcon,
          accountColor: _selectedColor,
        );
      } else {
        success = await _financialDataManager.addAccount(
          name: name,
          accountType: _selectedAccountType,
          balance: balance,
          bankName: bankName,
          accountNumber: accountNumber,
          accountIcon: _selectedIcon,
          accountColor: _selectedColor,
        );
      }

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_isEditMode 
                  ? 'Account updated successfully' 
                  : 'Account created successfully'),
              backgroundColor: AppTheme.successGreen,
            ),
          );
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_financialDataManager.error ?? 
                  (_isEditMode ? 'Failed to update account' : 'Failed to create account')),
              backgroundColor: AppTheme.errorRed,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.backgroundDark : AppTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Text(
        _isEditMode ? 'Edit Account' : 'Add Account',
        style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        Container(
          margin: EdgeInsets.only(right: 4.w),
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveAccount,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryTeal,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    _isEditMode ? 'Update' : 'Save',
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account Name
            _buildTextField(
              controller: _nameController,
              label: 'Account Name',
              hint: 'e.g., Chase Checking',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Account name is required';
                }
                return null;
              },
            ),
            
            SizedBox(height: 3.h),
            
            // Account Type
            AccountTypeSelector(
              selectedType: _selectedAccountType,
              accountTypes: _accountTypes,
              onTypeSelected: (type) {
                setState(() {
                  _selectedAccountType = type;
                });
              },
            ),
            
            SizedBox(height: 3.h),
            
            // Initial Balance
            _buildTextField(
              controller: _balanceController,
              label: 'Initial Balance',
              hint: '0.00',
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final amount = double.tryParse(value);
                  if (amount == null) {
                    return 'Please enter a valid amount';
                  }
                }
                return null;
              },
            ),
            
            SizedBox(height: 3.h),
            
            // Bank Name (Optional)
            _buildTextField(
              controller: _bankNameController,
              label: 'Bank Name (Optional)',
              hint: 'e.g., Chase Bank',
            ),
            
            SizedBox(height: 3.h),
            
            // Account Number (Optional)
            _buildTextField(
              controller: _accountNumberController,
              label: 'Account Number (Optional)',
              hint: 'Last 4 digits',
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(4),
              ],
            ),
            
            SizedBox(height: 3.h),
            
            // Account Icon
            AccountIconSelector(
              selectedIcon: _selectedIcon,
              iconOptions: _iconOptions,
              onIconSelected: (icon) {
                setState(() {
                  _selectedIcon = icon;
                });
              },
            ),
            
            SizedBox(height: 3.h),
            
            // Account Color
            AccountColorSelector(
              selectedColor: _selectedColor,
              colorOptions: _colorOptions,
              onColorSelected: (color) {
                setState(() {
                  _selectedColor = color;
                });
              },
            ),
            
            SizedBox(height: 5.h),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : AppTheme.textHighEmphasisLight,
          ),
        ),
        SizedBox(height: 1.h),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.textLowEmphasisLight,
            ),
            filled: true,
            fillColor: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.borderSubtle),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.borderSubtle),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.primaryTeal, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppTheme.errorRed),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
          ),
        ),
      ],
    );
  }
}
