import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../core/app_export.dart';
import '../core/services/connectivity_service.dart';
import '../core/services/data_sync_service.dart';

/// Professional offline indicator banner widget
class OfflineIndicator extends StatefulWidget {
  final Widget child;
  final bool showWhenOnline;

  const OfflineIndicator({
    Key? key,
    required this.child,
    this.showWhenOnline = false,
  }) : super(key: key);

  @override
  State<OfflineIndicator> createState() => _OfflineIndicatorState();
}

class _OfflineIndicatorState extends State<OfflineIndicator>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Listen to connectivity changes
    ConnectivityService.instance.addListener(_onConnectivityChanged);
    DataSyncService.instance.addListener(_onSyncStateChanged);

    // Initial state check
    _updateIndicatorVisibility();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _pulseController.dispose();
    ConnectivityService.instance.removeListener(_onConnectivityChanged);
    DataSyncService.instance.removeListener(_onSyncStateChanged);
    super.dispose();
  }

  void _onConnectivityChanged() {
    _updateIndicatorVisibility();
  }

  void _onSyncStateChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  void _updateIndicatorVisibility() {
    if (!mounted) return;

    final connectivity = ConnectivityService.instance;
    final shouldShow = !connectivity.isOnline || 
                      (widget.showWhenOnline && connectivity.isOnline);

    if (shouldShow && !_slideController.isCompleted) {
      _slideController.forward();
      if (!connectivity.isOnline) {
        _pulseController.repeat(reverse: true);
      }
    } else if (!shouldShow && _slideController.isCompleted) {
      _slideController.reverse();
      _pulseController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _shouldShowBanner() ? null : 0,
            child: _buildIndicatorBanner(),
          ),
          Expanded(child: widget.child),
        ],
      ),
    );
  }

  bool _shouldShowBanner() {
    final connectivity = ConnectivityService.instance;
    final syncService = DataSyncService.instance;

    return !connectivity.isOnline ||
           (syncService.isSyncing && connectivity.isOnline) ||
           (widget.showWhenOnline && connectivity.isOnline);
  }

  Widget _buildIndicatorBanner() {
    final connectivity = ConnectivityService.instance;
    final syncService = DataSyncService.instance;
    
    if (connectivity.isOnline) {
      if (syncService.isSyncing) {
        return _buildSyncingBanner();
      } else if (widget.showWhenOnline) {
        return _buildOnlineBanner();
      } else {
        return const SizedBox.shrink();
      }
    } else {
      return _buildOfflineBanner();
    }
  }

  Widget _buildOfflineBanner() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
      decoration: BoxDecoration(
        color: Colors.orange.shade600,
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_off_rounded,
              color: Colors.white,
              size: 4.w,
            ),
            SizedBox(width: 2.w),
            Text(
              'You\'re offline',
              style: TextStyle(
                color: Colors.white,
                fontSize: 13.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncingBanner() {
    final syncService = DataSyncService.instance;
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryTeal.withOpacity(0.8),
            AppTheme.primaryTeal,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryTeal.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            SizedBox(
              width: 5.w,
              height: 5.w,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                value: syncService.syncProgress > 0 ? syncService.syncProgress : null,
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Syncing data...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (syncService.syncProgress > 0) ...[
                    SizedBox(height: 0.5.h),
                    Text(
                      '${(syncService.syncProgress * 100).toInt()}% complete',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 11.sp,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnlineBanner() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
      decoration: BoxDecoration(
        color: Colors.green.shade600,
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_done_rounded,
              color: Colors.white,
              size: 4.w,
            ),
            SizedBox(width: 2.w),
            Text(
              'Connected - All data synced',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Compact offline status widget for use in app bars or other small spaces
class CompactOfflineStatus extends StatelessWidget {
  const CompactOfflineStatus({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: ConnectivityService.instance,
      builder: (context, child) {
        final connectivity = ConnectivityService.instance;
        
        if (connectivity.isOnline) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: Colors.orange.shade600,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.cloud_off_rounded,
                color: Colors.white,
                size: 3.w,
              ),
              SizedBox(width: 1.w),
              Text(
                'Offline',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Sync status indicator for showing sync progress
class SyncStatusIndicator extends StatelessWidget {
  const SyncStatusIndicator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: DataSyncService.instance,
      builder: (context, child) {
        final syncService = DataSyncService.instance;
        
        if (!syncService.isSyncing) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: EdgeInsets.all(2.w),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 4.w,
                height: 4.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryTeal),
                  value: syncService.syncProgress > 0 ? syncService.syncProgress : null,
                ),
              ),
              SizedBox(width: 2.w),
              Text(
                'Syncing...',
                style: TextStyle(
                  color: AppTheme.primaryTeal,
                  fontSize: 11.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
