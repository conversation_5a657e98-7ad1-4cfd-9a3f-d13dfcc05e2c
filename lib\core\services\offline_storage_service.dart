import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import '../models/transaction_model.dart' as TransactionModel;
import '../models/account_model.dart';
import '../models/category_model.dart' as CategoryModel;
import '../models/user_model.dart';
import '../models/sync_models.dart';
import 'local_database_service.dart';

/// Offline storage service for financial data with sync capabilities
class OfflineStorageService {
  static OfflineStorageService? _instance;
  final LocalDatabaseService _dbService = LocalDatabaseService.instance;

  /// Singleton instance
  static OfflineStorageService get instance {
    _instance ??= OfflineStorageService._internal();
    return _instance!;
  }

  OfflineStorageService._internal();

  // ==================== USER OPERATIONS ====================

  /// Save user data locally
  Future<bool> saveUser(User user) async {
    try {
      final db = await _dbService.database;
      final data = {
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'email_verified_at': user.emailVerifiedAt?.toIso8601String(),
        'created_at': user.createdAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
        'updated_at': user.updatedAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
        'last_synced': DateTime.now().toIso8601String(),
      };

      await db.insert(
        'users',
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      debugPrint('✅ User saved locally: ${user.email}');
      return true;
    } catch (e) {
      debugPrint('❌ Error saving user locally: $e');
      return false;
    }
  }

  /// Get user data from local storage
  Future<User?> getUser(int userId) async {
    try {
      final db = await _dbService.database;
      final results = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
        limit: 1,
      );

      if (results.isNotEmpty) {
        final data = results.first;
        return User(
          id: data['id'] as int,
          name: data['name'] as String,
          email: data['email'] as String,
          emailVerifiedAt: data['email_verified_at'] != null 
              ? DateTime.tryParse(data['email_verified_at'] as String)
              : null,
          createdAt: data['created_at'] != null 
              ? DateTime.tryParse(data['created_at'] as String)
              : null,
          updatedAt: data['updated_at'] != null 
              ? DateTime.tryParse(data['updated_at'] as String)
              : null,
        );
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting user locally: $e');
      return null;
    }
  }

  // ==================== TRANSACTION OPERATIONS ====================

  /// Save transaction locally
  Future<bool> saveTransaction(TransactionModel.Transaction transaction, {SyncStatus syncStatus = SyncStatus.synced}) async {
    try {
      final db = await _dbService.database;
      final data = {
        'id': transaction.id,
        'user_id': transaction.userId,
        'type': transaction.type,
        'amount': transaction.amount,
        'category': transaction.category,
        'subcategory': transaction.subcategory,
        'account': transaction.account,
        'description': transaction.description,
        'notes': transaction.notes,
        'date': transaction.date.toIso8601String(),
        'is_recurring': transaction.isRecurring ? 1 : 0,
        'recurring_frequency': transaction.recurringFrequency,
        'recurring_end_date': transaction.recurringEndDate?.toIso8601String(),
        'tags': transaction.tags,
        'location': transaction.location,
        'receipt': transaction.receipt,
        'created_at': transaction.createdAt.toIso8601String(),
        'updated_at': transaction.updatedAt.toIso8601String(),
        'last_synced': syncStatus == SyncStatus.synced ? DateTime.now().toIso8601String() : null,
        'sync_status': syncStatus.value,
      };

      await db.insert(
        'transactions',
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      debugPrint('✅ Transaction saved locally: ${transaction.id}');
      return true;
    } catch (e) {
      debugPrint('❌ Error saving transaction locally: $e');
      return false;
    }
  }

  /// Get all transactions for a user
  Future<List<TransactionModel.Transaction>> getTransactions(int userId, {int? limit, int? offset}) async {
    try {
      final db = await _dbService.database;
      final results = await db.query(
        'transactions',
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'date DESC, created_at DESC',
        limit: limit,
        offset: offset,
      );

      return results.map((data) => _transactionFromMap(data)).toList();
    } catch (e) {
      debugPrint('❌ Error getting transactions locally: $e');
      return [];
    }
  }

  /// Get transaction by ID
  Future<TransactionModel.Transaction?> getTransaction(int transactionId) async {
    try {
      final db = await _dbService.database;
      final results = await db.query(
        'transactions',
        where: 'id = ?',
        whereArgs: [transactionId],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return _transactionFromMap(results.first);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting transaction locally: $e');
      return null;
    }
  }

  /// Update transaction locally
  Future<bool> updateTransaction(TransactionModel.Transaction transaction, {SyncStatus syncStatus = SyncStatus.pending}) async {
    try {
      final db = await _dbService.database;
      final data = {
        'type': transaction.type,
        'amount': transaction.amount,
        'category': transaction.category,
        'subcategory': transaction.subcategory,
        'account': transaction.account,
        'description': transaction.description,
        'notes': transaction.notes,
        'date': transaction.date.toIso8601String(),
        'is_recurring': transaction.isRecurring ? 1 : 0,
        'recurring_frequency': transaction.recurringFrequency,
        'recurring_end_date': transaction.recurringEndDate?.toIso8601String(),
        'tags': transaction.tags,
        'location': transaction.location,
        'receipt': transaction.receipt,
        'updated_at': DateTime.now().toIso8601String(),
        'sync_status': syncStatus.value,
      };

      final rowsAffected = await db.update(
        'transactions',
        data,
        where: 'id = ?',
        whereArgs: [transaction.id],
      );

      debugPrint('✅ Transaction updated locally: ${transaction.id}');
      return rowsAffected > 0;
    } catch (e) {
      debugPrint('❌ Error updating transaction locally: $e');
      return false;
    }
  }

  /// Delete transaction locally
  Future<bool> deleteTransaction(int transactionId, {SyncStatus syncStatus = SyncStatus.pending}) async {
    try {
      final db = await _dbService.database;
      
      if (syncStatus == SyncStatus.pending) {
        // Mark as deleted but keep for sync
        final rowsAffected = await db.update(
          'transactions',
          {
            'sync_status': 'deleted',
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [transactionId],
        );
        return rowsAffected > 0;
      } else {
        // Actually delete from local storage
        final rowsAffected = await db.delete(
          'transactions',
          where: 'id = ?',
          whereArgs: [transactionId],
        );
        return rowsAffected > 0;
      }
    } catch (e) {
      debugPrint('❌ Error deleting transaction locally: $e');
      return false;
    }
  }

  // ==================== ACCOUNT OPERATIONS ====================

  /// Save account locally
  Future<bool> saveAccount(Account account, {SyncStatus syncStatus = SyncStatus.synced}) async {
    try {
      final db = await _dbService.database;
      final data = {
        'id': account.id,
        'user_id': account.userId,
        'name': account.name,
        'type': account.type,
        'balance': account.balance,
        'currency': account.currency,
        'bank_name': account.bankName,
        'account_number': account.accountNumber,
        'description': account.description,
        'is_active': account.isActive ? 1 : 0,
        'icon': account.icon,
        'color': account.color,
        'created_at': account.createdAt.toIso8601String(),
        'updated_at': account.updatedAt.toIso8601String(),
        'last_synced': syncStatus == SyncStatus.synced ? DateTime.now().toIso8601String() : null,
        'sync_status': syncStatus.value,
      };

      await db.insert(
        'accounts',
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      debugPrint('✅ Account saved locally: ${account.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Error saving account locally: $e');
      return false;
    }
  }

  /// Get all accounts for a user
  Future<List<Account>> getAccounts(int userId) async {
    try {
      final db = await _dbService.database;
      final results = await db.query(
        'accounts',
        where: 'user_id = ? AND sync_status != ?',
        whereArgs: [userId, 'deleted'],
        orderBy: 'name ASC',
      );

      return results.map((data) => _accountFromMap(data)).toList();
    } catch (e) {
      debugPrint('❌ Error getting accounts locally: $e');
      return [];
    }
  }

  /// Helper method to create Transaction from map
  TransactionModel.Transaction _transactionFromMap(Map<String, dynamic> data) {
    return TransactionModel.Transaction(
      id: data['id'] as int?,
      userId: data['user_id'] as int,
      type: data['type'] as String,
      amount: (data['amount'] as num).toDouble(),
      category: data['category'] as String,
      subcategory: data['subcategory'] as String?,
      account: data['account'] as String,
      description: data['description'] as String?,
      notes: data['notes'] as String?,
      date: DateTime.parse(data['date'] as String),
      isRecurring: (data['is_recurring'] as int) == 1,
      recurringFrequency: data['recurring_frequency'] as String?,
      recurringEndDate: data['recurring_end_date'] != null 
          ? DateTime.tryParse(data['recurring_end_date'] as String)
          : null,
      tags: data['tags'] as String?,
      location: data['location'] as String?,
      receipt: data['receipt'] as String?,
      createdAt: DateTime.parse(data['created_at'] as String),
      updatedAt: DateTime.parse(data['updated_at'] as String),
    );
  }

  /// Helper method to create Account from map
  Account _accountFromMap(Map<String, dynamic> data) {
    return Account(
      id: data['id'] as int?,
      userId: data['user_id'] as int,
      name: data['name'] as String,
      type: data['type'] as String,
      balance: (data['balance'] as num).toDouble(),
      currency: data['currency'] as String? ?? 'INR',
      bankName: data['bank_name'] as String?,
      accountNumber: data['account_number'] as String?,
      description: data['description'] as String?,
      isActive: (data['is_active'] as int) == 1,
      icon: data['icon'] as String?,
      color: data['color'] as String?,
      createdAt: DateTime.parse(data['created_at'] as String),
      updatedAt: DateTime.parse(data['updated_at'] as String),
    );
  }

  // ==================== CATEGORY OPERATIONS ====================

  /// Save category locally
  Future<bool> saveCategory(CategoryModel.Category category, {SyncStatus syncStatus = SyncStatus.synced}) async {
    try {
      final db = await _dbService.database;
      final data = {
        'id': category.id,
        'user_id': category.userId,
        'name': category.name,
        'type': category.type,
        'parent_category': category.parentCategory,
        'description': category.description,
        'icon': category.icon,
        'color': category.color,
        'is_active': category.isActive ? 1 : 0,
        'is_system': category.isSystem ? 1 : 0,
        'sort_order': category.sortOrder,
        'created_at': category.createdAt.toIso8601String(),
        'updated_at': category.updatedAt.toIso8601String(),
        'last_synced': syncStatus == SyncStatus.synced ? DateTime.now().toIso8601String() : null,
        'sync_status': syncStatus.value,
      };

      await db.insert(
        'categories',
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      debugPrint('✅ Category saved locally: ${category.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Error saving category locally: $e');
      return false;
    }
  }

  /// Get all categories for a user
  Future<List<CategoryModel.Category>> getCategories(int userId) async {
    try {
      final db = await _dbService.database;
      final results = await db.query(
        'categories',
        where: '(user_id = ? OR is_system = 1) AND sync_status != ?',
        whereArgs: [userId, 'deleted'],
        orderBy: 'sort_order ASC, name ASC',
      );

      return results.map((data) => _categoryFromMap(data)).toList();
    } catch (e) {
      debugPrint('❌ Error getting categories locally: $e');
      return [];
    }
  }

  /// Helper method to create Category from map
  CategoryModel.Category _categoryFromMap(Map<String, dynamic> data) {
    return CategoryModel.Category(
      id: data['id'] as int?,
      userId: data['user_id'] as int?,
      name: data['name'] as String,
      type: data['type'] as String,
      parentCategory: data['parent_category'] as String?,
      description: data['description'] as String?,
      icon: data['icon'] as String?,
      color: data['color'] as String?,
      isActive: (data['is_active'] as int) == 1,
      isSystem: (data['is_system'] as int) == 1,
      sortOrder: data['sort_order'] as int? ?? 0,
      createdAt: DateTime.parse(data['created_at'] as String),
      updatedAt: DateTime.parse(data['updated_at'] as String),
    );
  }

  // ==================== SYNC QUEUE OPERATIONS ====================

  /// Add item to sync queue
  Future<bool> addToSyncQueue(SyncQueueItem item) async {
    try {
      final db = await _dbService.database;
      await db.insert('sync_queue', item.toJson());
      debugPrint('✅ Added to sync queue: ${item.tableName} ${item.operation.value}');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding to sync queue: $e');
      return false;
    }
  }

  /// Get pending sync queue items
  Future<List<SyncQueueItem>> getPendingSyncItems({int? limit}) async {
    try {
      final db = await _dbService.database;
      final results = await db.query(
        'sync_queue',
        orderBy: 'created_at ASC',
        limit: limit,
      );

      return results.map((data) => SyncQueueItem.fromJson(data)).toList();
    } catch (e) {
      debugPrint('❌ Error getting sync queue items: $e');
      return [];
    }
  }

  /// Remove item from sync queue
  Future<bool> removeFromSyncQueue(int queueItemId) async {
    try {
      final db = await _dbService.database;
      final rowsAffected = await db.delete(
        'sync_queue',
        where: 'id = ?',
        whereArgs: [queueItemId],
      );
      return rowsAffected > 0;
    } catch (e) {
      debugPrint('❌ Error removing from sync queue: $e');
      return false;
    }
  }

  /// Update sync queue item retry count
  Future<bool> updateSyncQueueRetry(int queueItemId, int retryCount) async {
    try {
      final db = await _dbService.database;
      final rowsAffected = await db.update(
        'sync_queue',
        {'retry_count': retryCount},
        where: 'id = ?',
        whereArgs: [queueItemId],
      );
      return rowsAffected > 0;
    } catch (e) {
      debugPrint('❌ Error updating sync queue retry: $e');
      return false;
    }
  }

  /// Clear sync queue
  Future<bool> clearSyncQueue() async {
    try {
      final db = await _dbService.database;
      await db.delete('sync_queue');
      debugPrint('✅ Sync queue cleared');
      return true;
    } catch (e) {
      debugPrint('❌ Error clearing sync queue: $e');
      return false;
    }
  }

  // ==================== UTILITY METHODS ====================

  /// Get sync statistics
  Future<SyncStats> getSyncStats(int userId) async {
    try {
      final db = await _dbService.database;

      // Count transactions by sync status
      final transactionStats = await db.rawQuery('''
        SELECT sync_status, COUNT(*) as count
        FROM transactions
        WHERE user_id = ?
        GROUP BY sync_status
      ''', [userId]);

      // Count accounts by sync status
      final accountStats = await db.rawQuery('''
        SELECT sync_status, COUNT(*) as count
        FROM accounts
        WHERE user_id = ?
        GROUP BY sync_status
      ''', [userId]);

      // Count categories by sync status
      final categoryStats = await db.rawQuery('''
        SELECT sync_status, COUNT(*) as count
        FROM categories
        WHERE user_id = ? OR is_system = 1
        GROUP BY sync_status
      ''', [userId]);

      int totalRecords = 0;
      int syncedRecords = 0;
      int pendingRecords = 0;
      int failedRecords = 0;
      int conflictRecords = 0;

      // Process all stats
      for (final stats in [transactionStats, accountStats, categoryStats]) {
        for (final row in stats) {
          final status = row['sync_status'] as String;
          final count = row['count'] as int;
          totalRecords += count;

          switch (status) {
            case 'synced':
              syncedRecords += count;
              break;
            case 'pending':
              pendingRecords += count;
              break;
            case 'failed':
              failedRecords += count;
              break;
            case 'conflict':
              conflictRecords += count;
              break;
          }
        }
      }

      return SyncStats(
        totalRecords: totalRecords,
        syncedRecords: syncedRecords,
        pendingRecords: pendingRecords,
        failedRecords: failedRecords,
        conflictRecords: conflictRecords,
      );
    } catch (e) {
      debugPrint('❌ Error getting sync stats: $e');
      return const SyncStats(
        totalRecords: 0,
        syncedRecords: 0,
        pendingRecords: 0,
        failedRecords: 0,
        conflictRecords: 0,
      );
    }
  }

  /// Clear all data for a user
  Future<bool> clearUserData(int userId) async {
    try {
      final db = await _dbService.database;
      await db.transaction((txn) async {
        await txn.delete('transactions', where: 'user_id = ?', whereArgs: [userId]);
        await txn.delete('accounts', where: 'user_id = ?', whereArgs: [userId]);
        await txn.delete('categories', where: 'user_id = ?', whereArgs: [userId]);
        await txn.delete('users', where: 'id = ?', whereArgs: [userId]);
      });
      debugPrint('✅ User data cleared locally: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ Error clearing user data locally: $e');
      return false;
    }
  }
}
