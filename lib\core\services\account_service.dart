import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/account_model.dart';
import 'http_client_service.dart';

/// Service for managing user accounts
class AccountService {
  static AccountService? _instance;
  final HttpClientService _httpClient = HttpClientService.instance;

  /// Singleton instance
  static AccountService get instance {
    _instance ??= AccountService._internal();
    return _instance!;
  }

  AccountService._internal();

  /// Get all accounts for the authenticated user
  Future<ApiResponse<List<Account>>> getAccounts() async {
    try {
      final response = await _httpClient.get<List<Account>>(
        ApiConfig.accountsEndpoint,
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => Account.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Get accounts error: $e');
      return ApiResponse.error(
        message: 'Failed to get accounts',
        error: e.toString(),
      );
    }
  }

  /// Get a specific account by ID
  Future<ApiResponse<Account>> getAccount(int accountId) async {
    try {
      final response = await _httpClient.get<Account>(
        '${ApiConfig.accountsEndpoint}/$accountId',
        fromJson: (json) => Account.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Get account error: $e');
      return ApiResponse.error(
        message: 'Failed to get account',
        error: e.toString(),
      );
    }
  }

  /// Create a new account
  Future<ApiResponse<Account>> createAccount({
    required String name,
    required String type,
    required double initialBalance,
    String currency = 'INR',
    String? bankName,
    String? accountNumber,
    String? description,
    String? icon,
    String? color,
  }) async {
    try {
      final data = {
        'name': name,
        'type': type,
        'balance': initialBalance,
        'currency': currency,
      };

      if (bankName != null) data['bank_name'] = bankName;
      if (accountNumber != null) data['account_number'] = accountNumber;
      if (description != null) data['description'] = description;
      if (icon != null) data['icon'] = icon;
      if (color != null) data['color'] = color;

      final response = await _httpClient.post<Account>(
        ApiConfig.accountsEndpoint,
        data: data,
        fromJson: (json) => Account.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Create account error: $e');
      return ApiResponse.error(
        message: 'Failed to create account',
        error: e.toString(),
      );
    }
  }

  /// Update an existing account
  Future<ApiResponse<Account>> updateAccount({
    required int accountId,
    String? name,
    String? type,
    double? balance,
    String? currency,
    String? bankName,
    String? accountNumber,
    String? description,
    bool? isActive,
    String? icon,
    String? color,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (name != null) data['name'] = name;
      if (type != null) data['type'] = type;
      if (balance != null) data['balance'] = balance;
      if (currency != null) data['currency'] = currency;
      if (bankName != null) data['bank_name'] = bankName;
      if (accountNumber != null) data['account_number'] = accountNumber;
      if (description != null) data['description'] = description;
      if (isActive != null) data['is_active'] = isActive ? 1 : 0;
      if (icon != null) data['icon'] = icon;
      if (color != null) data['color'] = color;

      final response = await _httpClient.put<Account>(
        '${ApiConfig.accountsEndpoint}/$accountId',
        data: data,
        fromJson: (json) => Account.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Update account error: $e');
      return ApiResponse.error(
        message: 'Failed to update account',
        error: e.toString(),
      );
    }
  }

  /// Delete an account
  Future<ApiResponse<void>> deleteAccount(int accountId) async {
    try {
      final response = await _httpClient.delete<void>(
        '${ApiConfig.accountsEndpoint}/$accountId',
      );

      return response;
    } catch (e) {
      debugPrint('Delete account error: $e');
      return ApiResponse.error(
        message: 'Failed to delete account',
        error: e.toString(),
      );
    }
  }

  /// Get account balance
  Future<ApiResponse<double>> getAccountBalance(int accountId) async {
    try {
      final response = await _httpClient.get<double>(
        '${ApiConfig.accountsEndpoint}/$accountId/balance',
        fromJson: (json) => (json['balance'] is String) 
            ? double.parse(json['balance']) 
            : (json['balance'] as num).toDouble(),
      );

      return response;
    } catch (e) {
      debugPrint('Get account balance error: $e');
      return ApiResponse.error(
        message: 'Failed to get account balance',
        error: e.toString(),
      );
    }
  }

  /// Update account balance (usually done automatically by transactions)
  Future<ApiResponse<Account>> updateAccountBalance({
    required int accountId,
    required double newBalance,
  }) async {
    try {
      final response = await _httpClient.put<Account>(
        '${ApiConfig.accountsEndpoint}/$accountId/balance',
        data: {'balance': newBalance},
        fromJson: (json) => Account.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Update account balance error: $e');
      return ApiResponse.error(
        message: 'Failed to update account balance',
        error: e.toString(),
      );
    }
  }

  /// Get total balance across all accounts
  Future<ApiResponse<double>> getTotalBalance() async {
    try {
      final response = await _httpClient.get<double>(
        '${ApiConfig.accountsEndpoint}/total-balance',
        fromJson: (json) => (json['total_balance'] is String) 
            ? double.parse(json['total_balance']) 
            : (json['total_balance'] as num).toDouble(),
      );

      return response;
    } catch (e) {
      debugPrint('Get total balance error: $e');
      return ApiResponse.error(
        message: 'Failed to get total balance',
        error: e.toString(),
      );
    }
  }

  /// Get active accounts only
  Future<ApiResponse<List<Account>>> getActiveAccounts() async {
    try {
      final response = await _httpClient.get<List<Account>>(
        '${ApiConfig.accountsEndpoint}?active=1',
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => Account.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Get active accounts error: $e');
      return ApiResponse.error(
        message: 'Failed to get active accounts',
        error: e.toString(),
      );
    }
  }

  /// Get accounts by type
  Future<ApiResponse<List<Account>>> getAccountsByType(String type) async {
    try {
      final response = await _httpClient.get<List<Account>>(
        '${ApiConfig.accountsEndpoint}?type=$type',
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => Account.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Get accounts by type error: $e');
      return ApiResponse.error(
        message: 'Failed to get accounts by type',
        error: e.toString(),
      );
    }
  }

  /// Transfer money between accounts
  Future<ApiResponse<void>> transferBetweenAccounts({
    required int fromAccountId,
    required int toAccountId,
    required double amount,
    String? description,
  }) async {
    try {
      final data = <String, dynamic>{
        'from_account_id': fromAccountId,
        'to_account_id': toAccountId,
        'amount': amount,
      };

      if (description != null) data['description'] = description;

      final response = await _httpClient.post<void>(
        '${ApiConfig.accountsEndpoint}/transfer',
        data: data,
      );

      return response;
    } catch (e) {
      debugPrint('Transfer between accounts error: $e');
      return ApiResponse.error(
        message: 'Failed to transfer between accounts',
        error: e.toString(),
      );
    }
  }
}
