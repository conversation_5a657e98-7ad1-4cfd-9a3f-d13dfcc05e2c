import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../core/config/api_config.dart';
import '../../core/services/auth_state_manager.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoAnimationController;
  late AnimationController _loadingAnimationController;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoFadeAnimation;
  late Animation<double> _loadingAnimation;

  bool _isInitializing = true;
  String _initializationStatus = 'Initializing FinTrack...';
  AuthStateManager? _authStateManager;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }

  void _setupAnimations() {
    // Logo animation controller
    _logoAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Loading animation controller
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();

    // Logo scale animation
    _logoScaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoAnimationController,
      curve: Curves.elasticOut,
    ));

    // Logo fade animation
    _logoFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoAnimationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    // Loading indicator animation
    _loadingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    // Start logo animation
    _logoAnimationController.forward();
  }

  Future<void> _initializeApp() async {
    try {
      // Hide system status bar for immersive experience
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

      // Initialize API configuration
      if (mounted) {
        setState(() {
          _initializationStatus = 'Initializing API configuration...';
        });
      }
      await ApiConfig.initialize();

      // Initialize auth state manager after API config is ready
      if (mounted) {
        setState(() {
          _initializationStatus = 'Initializing authentication...';
        });
      }
      _authStateManager = AuthStateManager.instance;

      // Initialize and check authentication status
      if (mounted) {
        setState(() {
          _initializationStatus = 'Checking authentication...';
        });
      }
      await _authStateManager!.initializeAuthState();

      // Start preloading financial data if user is authenticated
      if (_authStateManager!.isAuthenticated) {
        if (mounted) {
          setState(() {
            _initializationStatus = 'Preparing your financial data...';
          });
        }

        // Start financial data initialization in background
        final financialDataManager = FinancialDataManager.instance;
        financialDataManager.initialize().catchError((error) {
          debugPrint('Background data loading error: $error');
        });
      }

      // Loading user preferences
      if (mounted) {
        setState(() {
          _initializationStatus = 'Loading preferences...';
        });
      }

      // Minimum splash time for smooth UX (reduced from 2.7s to 0.8s)
      await Future.delayed(const Duration(milliseconds: 800));

      if (mounted) {
        setState(() {
          _isInitializing = false;
        });

        // Navigate immediately - data loading continues in background
        if (mounted) {
          await _navigateToNextScreen();
        }
      }
    } catch (e) {
      // Handle initialization errors gracefully
      if (mounted) {
        setState(() {
          _initializationStatus = 'Preparing your experience...';
        });

        // Retry after delay
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          await _navigateToNextScreen();
        }
      }
    }
  }

  Future<void> _navigateToNextScreen() async {
    // Restore system UI before navigation
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // Check real authentication status asynchronously
    bool isAuthenticated = false;
    if (_authStateManager != null) {
      // Wait for auth state to be properly initialized
      await _authStateManager!.checkAuthStatus();
      isAuthenticated = _authStateManager!.isAuthenticated;
    }

    if (isAuthenticated) {
      // Navigate to dashboard for authenticated users
      Navigator.pushReplacementNamed(context, '/dashboard-screen');
    } else {
      // Navigate to authentication for unauthenticated users
      Navigator.pushReplacementNamed(context, '/authentication-screen');
    }
  }

  @override
  void dispose() {
    _logoAnimationController.dispose();
    _loadingAnimationController.dispose();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Spacer to push content to center
              const Spacer(flex: 2),

              // Animated Logo Section
              AnimatedBuilder(
                animation: _logoAnimationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _logoScaleAnimation.value,
                    child: Opacity(
                      opacity: _logoFadeAnimation.value,
                      child: _buildLogo(),
                    ),
                  );
                },
              ),

              SizedBox(height: 8.h),

              // App Name
              AnimatedBuilder(
                animation: _logoAnimationController,
                builder: (context, child) {
                  return Opacity(
                    opacity: _logoFadeAnimation.value,
                    child: Text(
                      'FinTrack',
                      style:
                          AppTheme.lightTheme.textTheme.headlineLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        letterSpacing: -0.5,
                      ),
                    ),
                  );
                },
              ),

              SizedBox(height: 2.h),

              // Tagline
              AnimatedBuilder(
                animation: _logoAnimationController,
                builder: (context, child) {
                  return Opacity(
                    opacity: _logoFadeAnimation.value * 0.8,
                    child: Text(
                      'Your Personal Finance Companion',
                      style: AppTheme.lightTheme.textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  );
                },
              ),

              const Spacer(flex: 1),

              // Loading Section
              if (_isInitializing) ...[
                // Loading Indicator
                SizedBox(
                  width: 8.w,
                  height: 8.w,
                  child: AnimatedBuilder(
                    animation: _loadingAnimation,
                    builder: (context, child) {
                      return CircularProgressIndicator(
                        value: null,
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white.withValues(alpha: 0.8),
                        ),
                      );
                    },
                  ),
                ),

                SizedBox(height: 3.h),

                // Status Text
                Text(
                  _initializationStatus,
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ] else ...[
                // Success Indicator
                Container(
                  width: 12.w,
                  height: 12.w,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: CustomIconWidget(
                    iconName: 'check',
                    color: Colors.white,
                    size: 6.w,
                  ),
                ),

                SizedBox(height: 2.h),

                Text(
                  'Ready to go!',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],

              SizedBox(height: 8.h),

              // Bottom spacing
              const Spacer(flex: 1),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Container(
      width: 25.w,
      height: 25.w,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background circle
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
          ),

          // Main logo icon
          CustomIconWidget(
            iconName: 'account_balance_wallet',
            color: Colors.white,
            size: 12.w,
          ),

          // Accent dot
          Positioned(
            top: 6.w,
            right: 6.w,
            child: Container(
              width: 3.w,
              height: 3.w,
              decoration: BoxDecoration(
                color: AppTheme.primaryTeal,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryTeal.withValues(alpha: 0.5),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
