import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class AccountCardWidget extends StatelessWidget {
  final Account account;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onArchive;

  const AccountCardWidget({
    Key? key,
    required this.account,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onArchive,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final accountColor = Color(int.parse(account.accountColor.replaceFirst('#', '0xFF')));

    return Container(
      margin: EdgeInsets.only(bottom: 3.h),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: () => _showAccountOptions(context),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.borderSubtle,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.shadowLight.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Account Icon
                Container(
                  width: 12.w,
                  height: 12.w,
                  decoration: BoxDecoration(
                    color: accountColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: CustomIconWidget(
                      iconName: account.accountIcon,
                      color: accountColor,
                      size: 24,
                    ),
                  ),
                ),
                
                SizedBox(width: 4.w),
                
                // Account Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Account Name
                      Text(
                        account.displayName,
                        style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isDark ? Colors.white : AppTheme.textHighEmphasisLight,
                        ),
                      ),
                      
                      SizedBox(height: 0.5.h),
                      
                      // Account Type
                      Text(
                        account.accountTypeDisplay,
                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.textMediumEmphasisLight,
                        ),
                      ),
                      
                      // Bank Name (if available)
                      if (account.bankName != null && account.bankName!.isNotEmpty) ...[
                        SizedBox(height: 0.5.h),
                        Text(
                          account.bankName!,
                          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textLowEmphasisLight,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Account Balance
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      account.formattedBalance,
                      style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: account.balance >= 0 
                            ? AppTheme.successGreen 
                            : AppTheme.errorRed,
                      ),
                    ),
                    
                    SizedBox(height: 0.5.h),
                    
                    // Account Status
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                      decoration: BoxDecoration(
                        color: account.isActive 
                            ? AppTheme.successGreen.withValues(alpha: 0.1)
                            : AppTheme.neutralGray.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        account.isActive ? 'Active' : 'Inactive',
                        style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                          color: account.isActive 
                              ? AppTheme.successGreen 
                              : AppTheme.neutralGray,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showAccountOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 12.w,
              height: 0.5.h,
              margin: EdgeInsets.symmetric(vertical: 2.h),
              decoration: BoxDecoration(
                color: AppTheme.neutralGray.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Account name
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Text(
                account.displayName,
                style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            
            SizedBox(height: 2.h),
            
            // Options
            ListTile(
              leading: CustomIconWidget(
                iconName: 'edit',
                color: AppTheme.primaryTeal,
                size: 24,
              ),
              title: const Text('Edit Account'),
              onTap: () {
                Navigator.pop(context);
                onEdit?.call();
              },
            ),
            
            if (account.isActive)
              ListTile(
                leading: CustomIconWidget(
                  iconName: 'archive',
                  color: AppTheme.warningOrange,
                  size: 24,
                ),
                title: const Text('Archive Account'),
                onTap: () {
                  Navigator.pop(context);
                  onArchive?.call();
                },
              ),
            
            ListTile(
              leading: CustomIconWidget(
                iconName: 'delete',
                color: AppTheme.errorRed,
                size: 24,
              ),
              title: const Text('Delete Account'),
              onTap: () {
                Navigator.pop(context);
                onDelete?.call();
              },
            ),
            
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }
}
