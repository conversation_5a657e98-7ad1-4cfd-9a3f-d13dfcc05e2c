import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import '../models/transaction_model.dart';
import '../models/account_model.dart';
import '../models/category_model.dart';
import '../models/user_model.dart';

/// Local database service for offline data storage using SQLite
class LocalDatabaseService {
  static LocalDatabaseService? _instance;
  static Database? _database;

  /// Singleton instance
  static LocalDatabaseService get instance {
    _instance ??= LocalDatabaseService._internal();
    return _instance!;
  }

  LocalDatabaseService._internal();

  /// Get database instance
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database
  Future<Database> _initDatabase() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, 'fintrack_offline.db');
      
      debugPrint('📱 Initializing local database at: $path');
      
      return await openDatabase(
        path,
        version: 1,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );
    } catch (e) {
      debugPrint('❌ Database initialization error: $e');
      rethrow;
    }
  }

  /// Create database tables
  Future<void> _createDatabase(Database db, int version) async {
    debugPrint('📱 Creating database tables...');
    
    // Create users table
    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        email_verified_at TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT
      )
    ''');

    // Create accounts table
    await db.execute('''
      CREATE TABLE accounts (
        id INTEGER PRIMARY KEY,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        balance REAL NOT NULL DEFAULT 0.0,
        currency TEXT DEFAULT 'INR',
        bank_name TEXT,
        account_number TEXT,
        description TEXT,
        is_active INTEGER DEFAULT 1,
        icon TEXT,
        color TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT,
        sync_status TEXT DEFAULT 'synced',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Create categories table
    await db.execute('''
      CREATE TABLE categories (
        id INTEGER PRIMARY KEY,
        user_id INTEGER,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        parent_category TEXT,
        description TEXT,
        icon TEXT,
        color TEXT,
        is_active INTEGER DEFAULT 1,
        is_system INTEGER DEFAULT 0,
        sort_order INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT,
        sync_status TEXT DEFAULT 'synced',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Create transactions table
    await db.execute('''
      CREATE TABLE transactions (
        id INTEGER PRIMARY KEY,
        user_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        subcategory TEXT,
        account TEXT NOT NULL,
        description TEXT,
        notes TEXT,
        date TEXT NOT NULL,
        is_recurring INTEGER DEFAULT 0,
        recurring_frequency TEXT,
        recurring_end_date TEXT,
        tags TEXT,
        location TEXT,
        receipt TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT,
        sync_status TEXT DEFAULT 'synced',
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Create sync_queue table for pending operations
    await db.execute('''
      CREATE TABLE sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        operation TEXT NOT NULL,
        record_id INTEGER,
        data TEXT NOT NULL,
        created_at TEXT NOT NULL,
        retry_count INTEGER DEFAULT 0
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_accounts_user_id ON accounts (user_id)');
    await db.execute('CREATE INDEX idx_categories_user_id ON categories (user_id)');
    await db.execute('CREATE INDEX idx_transactions_user_id ON transactions (user_id)');
    await db.execute('CREATE INDEX idx_transactions_date ON transactions (date)');
    await db.execute('CREATE INDEX idx_sync_queue_table ON sync_queue (table_name)');
    
    debugPrint('✅ Database tables created successfully');
  }

  /// Upgrade database
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    debugPrint('📱 Upgrading database from version $oldVersion to $newVersion');
    // Handle database upgrades here when needed
  }

  /// Close database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      debugPrint('📱 Database closed');
    }
  }

  /// Clear all data (for logout)
  Future<void> clearAllData() async {
    try {
      final db = await database;
      await db.transaction((txn) async {
        await txn.delete('transactions');
        await txn.delete('accounts');
        await txn.delete('categories');
        await txn.delete('users');
        await txn.delete('sync_queue');
      });
      debugPrint('✅ All local data cleared');
    } catch (e) {
      debugPrint('❌ Error clearing local data: $e');
      rethrow;
    }
  }

  /// Get database file size
  Future<int> getDatabaseSize() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, 'fintrack_offline.db');
      final file = File(path);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      debugPrint('❌ Error getting database size: $e');
      return 0;
    }
  }

  /// Check if database exists
  Future<bool> databaseExists() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, 'fintrack_offline.db');
      final file = File(path);
      return await file.exists();
    } catch (e) {
      debugPrint('❌ Error checking database existence: $e');
      return false;
    }
  }

  /// Execute raw query (for debugging)
  Future<List<Map<String, dynamic>>> rawQuery(String sql) async {
    try {
      final db = await database;
      return await db.rawQuery(sql);
    } catch (e) {
      debugPrint('❌ Raw query error: $e');
      rethrow;
    }
  }
}
