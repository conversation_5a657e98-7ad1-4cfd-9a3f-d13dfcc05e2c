import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class DateSectionHeaderWidget extends StatelessWidget {
  final DateTime date;
  final double totalAmount;
  final int transactionCount;

  const DateSectionHeaderWidget({
    super.key,
    required this.date,
    required this.totalAmount,
    required this.transactionCount,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;

    return ListenableBuilder(
      listenable: FormattingService.instance,
      builder: (context, child) {
        return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        gradient: AppTheme.subtleGradient,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDark
              ? AppTheme.neutralGray.withValues(alpha: 0.2)
              : AppTheme.borderSubtle,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _formatDate(date),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isDark
                          ? AppTheme.textHighEmphasisDark
                          : AppTheme.textHighEmphasisLight,
                    ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                '$transactionCount transaction${transactionCount != 1 ? 's' : ''}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isDark
                          ? AppTheme.textMediumEmphasisDark
                          : AppTheme.textMediumEmphasisLight,
                    ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Net Amount',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isDark
                          ? AppTheme.textMediumEmphasisDark
                          : AppTheme.textMediumEmphasisLight,
                    ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                _formatAmount(totalAmount),
                style: AppTheme.financialAmountStyle(
                  isLight: !isDark,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ).copyWith(
                  color: totalAmount >= 0
                      ? AppTheme.successGreen
                      : AppTheme.alertRed,
                ),
              ),
            ],
          ),
        ],
      ),
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return FormattingService.instance.formatDateShort(date);
  }

  String _formatAmount(double amount) {
    final sign = amount >= 0 ? '+' : '-';
    final formattedAmount = FormattingService.instance.formatCurrency(amount.abs());
    return '$sign$formattedAmount';
  }
}
