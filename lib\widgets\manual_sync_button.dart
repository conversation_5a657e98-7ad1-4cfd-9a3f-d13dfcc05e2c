import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';

class ManualSyncButton extends StatefulWidget {
  final VoidCallback onPressed;
  final AnimationController? rotationController;

  const ManualSyncButton({
    Key? key,
    required this.onPressed,
    this.rotationController,
  }) : super(key: key);

  @override
  State<ManualSyncButton> createState() => _ManualSyncButtonState();
}

class _ManualSyncButtonState extends State<ManualSyncButton> {
  bool _hasPendingChanges = false;

  @override
  void initState() {
    super.initState();
    _checkPendingChanges();
    
    // Listen to sync service changes
    DataSyncService.instance.addListener(_onSyncServiceChanged);
  }

  @override
  void dispose() {
    DataSyncService.instance.removeListener(_onSyncServiceChanged);
    super.dispose();
  }

  void _onSyncServiceChanged() {
    _checkPendingChanges();
  }

  Future<void> _checkPendingChanges() async {
    final hasPending = await DataSyncService.instance.hasPendingChanges;
    if (mounted && hasPending != _hasPendingChanges) {
      setState(() {
        _hasPendingChanges = hasPending;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Stack(
      children: [
        // Main sync button
        RotationTransition(
          turns: widget.rotationController?.view ?? const AlwaysStoppedAnimation(0),
          child: IconButton(
            onPressed: widget.onPressed,
            icon: CustomIconWidget(
              iconName: 'sync',
              color: isDark
                  ? AppTheme.textMediumEmphasisDark
                  : AppTheme.textMediumEmphasisLight,
              size: 24,
            ),
          ),
        ),
        
        // Yellow dot indicator for pending changes
        if (_hasPendingChanges)
          Positioned(
            right: 8,
            top: 8,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: AppTheme.warningOrange,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.warningOrange.withOpacity(0.5),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
