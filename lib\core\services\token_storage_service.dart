import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import '../models/user_model.dart';

/// Service for managing authentication tokens and user data in local storage
class TokenStorageService {
  static TokenStorageService? _instance;
  SharedPreferences? _prefs;

  /// Singleton instance
  static TokenStorageService get instance {
    _instance ??= TokenStorageService._internal();
    return _instance!;
  }

  TokenStorageService._internal();

  /// Initialize SharedPreferences
  Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Save authentication token
  Future<bool> saveToken(String token) async {
    await _initPrefs();
    final success = await _prefs!.setString(ApiConfig.authTokenKey, token);
    if (success) {
      await _prefs!.setBool(ApiConfig.isLoggedInKey, true);
      // Calculate token expiry (24 hours from now as per PHP backend)
      final expiry = DateTime.now().add(const Duration(hours: 24));
      await _prefs!.setString(
        ApiConfig.tokenExpiryKey,
        expiry.toIso8601String(),
      );
    }
    return success;
  }

  /// Get authentication token
  Future<String?> getToken() async {
    await _initPrefs();
    
    // Check if token is expired
    if (await isTokenExpired()) {
      await clearToken();
      return null;
    }
    
    return _prefs!.getString(ApiConfig.authTokenKey);
  }

  /// Check if token exists and is valid
  Future<bool> hasValidToken() async {
    final token = await getToken();
    return token != null && token.isNotEmpty && !(await isTokenExpired());
  }

  /// Check if token is expired
  Future<bool> isTokenExpired() async {
    await _initPrefs();
    final expiryString = _prefs!.getString(ApiConfig.tokenExpiryKey);
    if (expiryString == null) return true;
    
    final expiry = DateTime.tryParse(expiryString);
    if (expiry == null) return true;
    
    return DateTime.now().isAfter(expiry);
  }

  /// Clear authentication token
  Future<bool> clearToken() async {
    await _initPrefs();
    final results = await Future.wait([
      _prefs!.remove(ApiConfig.authTokenKey),
      _prefs!.remove(ApiConfig.isLoggedInKey),
      _prefs!.remove(ApiConfig.tokenExpiryKey),
    ]);
    return results.every((result) => result);
  }

  /// Save user data
  Future<bool> saveUserData(User user) async {
    await _initPrefs();
    return await _prefs!.setString(
      ApiConfig.userDataKey,
      user.toJsonString(),
    );
  }

  /// Get user data
  Future<User?> getUserData() async {
    await _initPrefs();
    final userDataString = _prefs!.getString(ApiConfig.userDataKey);
    if (userDataString == null) return null;
    
    try {
      return User.fromJsonString(userDataString);
    } catch (e) {
      // If parsing fails, clear the corrupted data
      await clearUserData();
      return null;
    }
  }

  /// Clear user data
  Future<bool> clearUserData() async {
    await _initPrefs();
    return await _prefs!.remove(ApiConfig.userDataKey);
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    await _initPrefs();
    final isLoggedIn = _prefs!.getBool(ApiConfig.isLoggedInKey) ?? false;
    final hasToken = await hasValidToken();
    return isLoggedIn && hasToken;
  }

  /// Save complete authentication data (token + user)
  Future<bool> saveAuthData(String token, User user) async {
    final results = await Future.wait([
      saveToken(token),
      saveUserData(user),
    ]);
    return results.every((result) => result);
  }

  /// Clear all authentication data
  Future<bool> clearAllAuthData() async {
    final results = await Future.wait([
      clearToken(),
      clearUserData(),
    ]);
    return results.every((result) => result);
  }

  /// Get token expiry date
  Future<DateTime?> getTokenExpiry() async {
    await _initPrefs();
    final expiryString = _prefs!.getString(ApiConfig.tokenExpiryKey);
    if (expiryString == null) return null;
    return DateTime.tryParse(expiryString);
  }

  /// Get time until token expires
  Future<Duration?> getTimeUntilExpiry() async {
    final expiry = await getTokenExpiry();
    if (expiry == null) return null;
    
    final now = DateTime.now();
    if (now.isAfter(expiry)) return Duration.zero;
    
    return expiry.difference(now);
  }

  /// Check if token will expire soon (within 1 hour)
  Future<bool> willExpireSoon() async {
    final timeUntilExpiry = await getTimeUntilExpiry();
    if (timeUntilExpiry == null) return true;
    
    return timeUntilExpiry.inHours < 1;
  }
}
