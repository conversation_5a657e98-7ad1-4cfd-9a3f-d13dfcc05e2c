import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sizer/sizer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/app_export.dart';
import '../../core/services/ad_service.dart';
import './widgets/premium_features_widget.dart';
import './widgets/profile_section_widget.dart';
import './widgets/settings_group_widget.dart';
import './widgets/settings_dialogs.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late final AuthStateManager _authStateManager;
  final SettingsService _settingsService = SettingsService.instance;
  User? _currentUser;
  bool _isLoadingProfile = false;

  // Mock premium features data
  final List<Map<String, dynamic>> premiumFeatures = [
    {
      "id": "advanced_reports",
      "title": "Advanced Reports",
      "description":
          "Detailed analytics with custom date ranges and category breakdowns",
      "icon": "analytics",
      "isUnlocked": false,
    },
    {
      "id": "unlimited_accounts",
      "title": "Unlimited Accounts",
      "description":
          "Add unlimited bank accounts, wallets, and payment methods",
      "icon": "account_balance",
      "isUnlocked": true,
    },
    {
      "id": "cloud_backup",
      "title": "Cloud Backup",
      "description":
          "Automatic backup to cloud with cross-device synchronization",
      "icon": "cloud_upload",
      "isUnlocked": false,
    },
    {
      "id": "export_formats",
      "title": "Export Formats",
      "description": "Export data in multiple formats including PDF and Excel",
      "icon": "file_download",
      "isUnlocked": true,
    },
  ];

  @override
  void initState() {
    super.initState();
    _authStateManager = AuthStateManager.instance;
    _loadUserProfile();

    // Listen to authentication state changes
    _authStateManager.addListener(_onAuthStateChanged);
    _settingsService.addListener(_onSettingsChanged);
  }

  @override
  void dispose() {
    _authStateManager.removeListener(_onAuthStateChanged);
    _settingsService.removeListener(_onSettingsChanged);
    super.dispose();
  }

  /// Handle authentication state changes
  void _onAuthStateChanged() {
    if (mounted) {
      setState(() {
        _currentUser = _authStateManager.currentUser;
      });
    }
  }

  /// Handle settings changes
  void _onSettingsChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  /// Load user profile from authentication state
  Future<void> _loadUserProfile() async {
    setState(() {
      _isLoadingProfile = true;
    });

    try {
      // First try to get user from current auth state
      _currentUser = _authStateManager.currentUser;

      // If no user in state, try to refresh profile from API
      if (_currentUser == null && _authStateManager.isAuthenticated) {
        await _authStateManager.refreshProfile();
        _currentUser = _authStateManager.currentUser;
      }
    } catch (e) {
      debugPrint('Error loading user profile: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingProfile = false;
        });
      }
    }
  }

  /// Get user profile data for display
  Map<String, dynamic> get userProfile {
    if (_currentUser != null) {
      return {
        "name": _currentUser!.name,
        "email": _currentUser!.email,
        "avatar": null, // No avatar in current user model
        "memberSince": _formatMemberSince(_currentUser!.createdAt),
        "isEmailVerified": _currentUser!.isEmailVerified,
      };
    }

    // Fallback for unauthenticated users
    return {
      "name": "Guest User",
      "email": "<EMAIL>",
      "avatar": null,
      "memberSince": "Recently",
      "isEmailVerified": false,
    };
  }

  /// Format member since date
  String _formatMemberSince(DateTime? createdAt) {
    if (createdAt == null) return "Recently";

    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays < 30) {
      return "This month";
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return "$months month${months > 1 ? 's' : ''} ago";
    } else {
      final years = (difference.inDays / 365).floor();
      return "$years year${years > 1 ? 's' : ''} ago";
    }
  }





  void _onEditProfile() {
    if (_currentUser == null) {
      Fluttertoast.showToast(
        msg: "Please login to edit profile",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            CustomIconWidget(
              iconName: 'person',
              color: AppTheme.primaryTeal,
              size: 6.w,
            ),
            SizedBox(width: 2.w),
            Text("Profile Information"),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileInfoRow("Name", _currentUser!.name),
            SizedBox(height: 1.h),
            _buildProfileInfoRow("Email", _currentUser!.email),
            SizedBox(height: 1.h),
            _buildProfileInfoRow("Email Verified",
                _currentUser!.isEmailVerified ? "Yes" : "No"),
            SizedBox(height: 1.h),
            _buildProfileInfoRow("Member Since",
                _formatMemberSince(_currentUser!.createdAt)),
            if (_currentUser!.createdAt != null) ...[
              SizedBox(height: 1.h),
              _buildProfileInfoRow("Joined On",
                  "${_currentUser!.createdAt!.day}/${_currentUser!.createdAt!.month}/${_currentUser!.createdAt!.year}"),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Close',
              style: TextStyle(
                color: AppTheme.primaryTeal,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _refreshProfile();
            },
            child: Text(
              'Refresh',
              style: TextStyle(
                color: AppTheme.primaryTeal,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 25.w,
          child: Text(
            "$label:",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.neutralGray,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  /// Refresh user profile from server
  Future<void> _refreshProfile() async {
    if (!_authStateManager.isAuthenticated) {
      Fluttertoast.showToast(
        msg: "Not authenticated",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
      return;
    }

    setState(() {
      _isLoadingProfile = true;
    });

    try {
      final success = await _authStateManager.refreshProfile();
      if (success) {
        _currentUser = _authStateManager.currentUser;
        Fluttertoast.showToast(
          msg: "Profile refreshed successfully",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      } else {
        Fluttertoast.showToast(
          msg: "Failed to refresh profile",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: "Error refreshing profile",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingProfile = false;
        });
      }
    }
  }

  /// Handle user logout
  Future<void> _handleLogout() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Perform logout
      await _authStateManager.logout();

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show success message
      Fluttertoast.showToast(
        msg: "Logged out successfully",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );

      // Navigate to authentication screen
      if (mounted) {
        Navigator.pushNamedAndRemoveUntil(
          context,
          '/authentication-screen',
          (route) => false,
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      Fluttertoast.showToast(
        msg: "Logout failed: ${e.toString()}",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }



  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            CustomIconWidget(
              iconName: 'info',
              color: AppTheme.primaryPurple,
              size: 6.w,
            ),
            SizedBox(width: 2.w),
            Text("About FinTrack"),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Version: 1.0.0"),
            SizedBox(height: 1.h),
            Text("Build: 2025.07.19"),
            SizedBox(height: 2.h),
            Text(
              "FinTrack is your personal finance companion, helping you track expenses, manage budgets, and achieve financial goals.",
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 2.h),
            Text("© 2025 FinTrack. All rights reserved."),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text("Close"),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          "Settings",
          style: Theme.of(context).appBarTheme.titleTextStyle,
        ),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: Theme.of(context).colorScheme.onSurface,
            size: 6.w,
          ),
        ),
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 1.h),

            // Profile Section
            ProfileSectionWidget(
              userProfile: userProfile,
              onEditProfile: _onEditProfile,
            ),

            SizedBox(height: 2.h),

            // Appearance Settings
            SettingsGroupWidget(
              title: "APPEARANCE",
              items: [
                SettingsItemData(
                  title: "Theme",
                  subtitle: _getThemeSubtitle(_settingsService.themeMode),
                  iconName: 'palette',
                  iconColor: AppTheme.primaryTeal,
                  onTap: _showThemeDialog,
                ),
              ],
            ),

            SizedBox(height: 2.h),

            // Currency & Localization Settings
            SettingsGroupWidget(
              title: "CURRENCY & LOCALIZATION",
              items: [
                SettingsItemData(
                  title: "Currency",
                  subtitle: "${_settingsService.currency.name} (${_settingsService.currency.symbol})",
                  iconName: 'currency_exchange',
                  iconColor: AppTheme.successGreen,
                  onTap: _showCurrencyDialog,
                ),
                SettingsItemData(
                  title: "Date Format",
                  subtitle: _settingsService.dateFormat.format,
                  iconName: 'calendar_today',
                  iconColor: AppTheme.primaryPurple,
                  onTap: _showDateFormatDialog,
                ),
                SettingsItemData(
                  title: "Number System",
                  subtitle: _settingsService.numberFormat.format,
                  iconName: 'format_list_numbered',
                  iconColor: AppTheme.primaryTeal,
                  onTap: _showNumberFormatDialog,
                ),
              ],
            ),

            // Security Settings
            SettingsGroupWidget(
              title: "SECURITY",
              items: [
                SettingsItemData(
                  title: "Biometric Authentication",
                  subtitle: _settingsService.biometricEnabled ? "Enabled" : "Disabled",
                  iconName: 'fingerprint',
                  iconColor: AppTheme.primaryTeal,
                  trailing: Switch(
                    value: _settingsService.biometricEnabled,
                    onChanged: (value) {
                      if (value) {
                        _showBiometricComingSoon();
                      } else {
                        _settingsService.setBiometricEnabled(value);
                      }
                    },
                  ),
                  showArrow: false,
                ),
                SettingsItemData(
                  title: "Change Password",
                  subtitle: "Update your account password",
                  iconName: 'lock',
                  iconColor: AppTheme.alertRed,
                  onTap: _showChangePasswordDialog,
                ),
              ],
            ),

            // Notification Settings
            SettingsGroupWidget(
              title: "NOTIFICATIONS",
              items: [
                SettingsItemData(
                  title: "Push Notifications",
                  subtitle: _settingsService.notificationsEnabled ? "Enabled" : "Disabled",
                  iconName: 'notifications',
                  iconColor: AppTheme.primaryTeal,
                  trailing: Switch(
                    value: _settingsService.notificationsEnabled,
                    onChanged: (value) {
                      _settingsService.setNotificationsEnabled(value);
                    },
                  ),
                  showArrow: false,
                ),
              ],
            ),

            // Premium Features
            PremiumFeaturesWidget(
              premiumFeatures: premiumFeatures,
              onWatchAd: _onWatchAd,
            ),

            // About & Support
            SettingsGroupWidget(
              title: "ABOUT & SUPPORT",
              items: [
                SettingsItemData(
                  title: "App Version",
                  subtitle: "1.0.0 (Build 2025.07.19)",
                  iconName: 'info',
                  iconColor: AppTheme.primaryPurple,
                  onTap: _showAboutDialog,
                ),
                SettingsItemData(
                  title: "Privacy Policy",
                  subtitle: "Read our privacy policy",
                  iconName: 'privacy_tip',
                  iconColor: AppTheme.primaryTeal,
                  onTap: () {
                    Navigator.of(context).pushNamed('/privacy-policy-screen');
                  },
                ),
                SettingsItemData(
                  title: "Terms of Service",
                  subtitle: "Read our terms of service",
                  iconName: 'description',
                  iconColor: AppTheme.neutralGray,
                  onTap: () {
                    Navigator.of(context).pushNamed('/terms-of-service-screen');
                  },
                ),
                SettingsItemData(
                  title: "Contact Support",
                  subtitle: "Get help and support",
                  iconName: 'support_agent',
                  iconColor: AppTheme.successGreen,
                  onTap: () {
                    Fluttertoast.showToast(
                      msg: "Opening support chat...",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.BOTTOM,
                    );
                  },
                ),
                SettingsItemData(
                  title: "Rate App",
                  subtitle: "Rate us on the app store",
                  iconName: 'star_rate',
                  iconColor: AppTheme.primaryPurple,
                  onTap: _rateApp,
                ),
                SettingsItemData(
                  title: "Open Source Licenses",
                  subtitle: "View third-party licenses",
                  iconName: 'code',
                  iconColor: AppTheme.neutralGray,
                  onTap: () {
                    Navigator.of(context).pushNamed('/licenses-screen');
                  },
                ),
              ],
            ),



            // Logout Button
            Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text("Logout"),
                      content: Text(
                          "Are you sure you want to logout? Your data will be saved locally."),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text("Cancel"),
                        ),
                        ElevatedButton(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            await _handleLogout();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.alertRed,
                          ),
                          child: Text("Logout"),
                        ),
                      ],
                    ),
                  );
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.alertRed,
                  side: BorderSide(color: AppTheme.alertRed),
                  padding: EdgeInsets.symmetric(vertical: 2.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                icon: CustomIconWidget(
                  iconName: 'logout',
                  color: AppTheme.alertRed,
                  size: 5.w,
                ),
                label: Text(
                  "Logout",
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: AppTheme.alertRed,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ),

            SizedBox(height: 4.h),
          ],
        ),
      ),
    );
  }

  // ==================== PLACEHOLDER METHODS ====================

  /// Placeholder for watch ad functionality
  void _onWatchAd(String featureId) {
    AdService().showRewardedAd(() {
      setState(() {
        final feature = premiumFeatures.firstWhere((f) => f['id'] == featureId);
        feature['isUnlocked'] = true;
      });
      Fluttertoast.showToast(
        msg: "You've unlocked ${featureId.replaceAll('_', ' ')}!",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: AppTheme.successGreen,
        textColor: Colors.white,
      );
    });
  }

  // ==================== DIALOG METHODS ====================

  /// Show theme selection dialog
  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => ThemeSelectionDialog(
        currentTheme: _settingsService.themeMode,
        onThemeSelected: (theme) {
          _settingsService.setThemeMode(theme);
        },
      ),
    );
  }

  /// Show currency selection dialog
  void _showCurrencyDialog() {
    showDialog(
      context: context,
      builder: (context) => CurrencySelectionDialog(
        currentCurrency: _settingsService.currency,
        onCurrencySelected: (currency) {
          _settingsService.setCurrency(currency);
        },
      ),
    );
  }

  /// Show date format selection dialog
  void _showDateFormatDialog() {
    showDialog(
      context: context,
      builder: (context) => DateFormatSelectionDialog(
        currentFormat: _settingsService.dateFormat,
        onFormatSelected: (format) {
          _settingsService.setDateFormat(format);
        },
      ),
    );
  }

  /// Show number format selection dialog
  void _showNumberFormatDialog() {
    showDialog(
      context: context,
      builder: (context) => NumberFormatSelectionDialog(
        currentFormat: _settingsService.numberFormat,
        onFormatSelected: (format) {
          _settingsService.setNumberFormat(format);
        },
      ),
    );
  }

  /// Show change password dialog
  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => const ChangePasswordDialog(),
    );
  }

  /// Show biometric coming soon message
  void _showBiometricComingSoon() {
    Fluttertoast.showToast(
      msg: "Biometric authentication coming soon",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  /// Open Google Play Store to rate the app
  Future<void> _rateApp() async {
    const String packageId = 'com.fintrack.app';
    final Uri playStoreUri = Uri.parse('https://play.google.com/store/apps/details?id=$packageId');

    try {
      if (await canLaunchUrl(playStoreUri)) {
        await launchUrl(
          playStoreUri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        // Fallback to generic Play Store URL
        final Uri fallbackUri = Uri.parse('https://play.google.com/store');
        if (await canLaunchUrl(fallbackUri)) {
          await launchUrl(
            fallbackUri,
            mode: LaunchMode.externalApplication,
          );
        } else {
          throw 'Could not launch Play Store';
        }
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: "Unable to open Play Store. Please search for 'FinTrack' manually.",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  // ==================== HELPER METHODS ====================

  /// Get theme subtitle text
  String _getThemeSubtitle(AppThemeMode theme) {
    switch (theme) {
      case AppThemeMode.light:
        return 'Light theme';
      case AppThemeMode.dark:
        return 'Dark theme';
      case AppThemeMode.system:
        return 'Follow system setting';
    }
  }
}
