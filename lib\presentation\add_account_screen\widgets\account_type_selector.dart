import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class AccountTypeSelector extends StatelessWidget {
  final String selectedType;
  final List<Map<String, String>> accountTypes;
  final Function(String) onTypeSelected;

  const AccountTypeSelector({
    Key? key,
    required this.selectedType,
    required this.accountTypes,
    required this.onTypeSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Type',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : AppTheme.textHighEmphasisLight,
          ),
        ),
        SizedBox(height: 2.h),
        Wrap(
          spacing: 3.w,
          runSpacing: 2.h,
          children: accountTypes.map((type) {
            final isSelected = selectedType == type['value'];
            return _buildTypeChip(
              value: type['value']!,
              label: type['label']!,
              icon: type['icon']!,
              isSelected: isSelected,
              isDark: isDark,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTypeChip({
    required String value,
    required String label,
    required String icon,
    required bool isSelected,
    required bool isDark,
  }) {
    return GestureDetector(
      onTap: () => onTypeSelected(value),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryTeal.withValues(alpha: 0.1)
              : (isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.primaryTeal : AppTheme.borderSubtle,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomIconWidget(
              iconName: icon,
              color: isSelected ? AppTheme.primaryTeal : AppTheme.textMediumEmphasisLight,
              size: 20,
            ),
            SizedBox(width: 2.w),
            Text(
              label,
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: isSelected 
                    ? AppTheme.primaryTeal 
                    : (isDark ? Colors.white : AppTheme.textHighEmphasisLight),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
