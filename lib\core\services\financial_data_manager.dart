import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/transaction_model.dart';
import '../models/account_model.dart';
import '../models/category_model.dart' as CategoryModel;
import '../models/financial_summary_model.dart';
import '../models/sync_models.dart';
import '../models/user_model.dart';
import 'http_client_service.dart';
import 'transaction_service.dart';
import 'account_service.dart';
import 'category_service.dart';
import 'connectivity_service.dart';
import 'offline_storage_service.dart';
import 'data_sync_service.dart';
import 'auth_service.dart';
import 'auth_state_manager.dart';

/// Central manager for all financial data with offline support
class FinancialDataManager extends ChangeNotifier {
  static FinancialDataManager? _instance;

  final HttpClientService _httpClient = HttpClientService.instance;
  final TransactionService _transactionService = TransactionService.instance;
  final AccountService _accountService = AccountService.instance;
  final CategoryService _categoryService = CategoryService.instance;
  final ConnectivityService _connectivity = ConnectivityService.instance;
  final OfflineStorageService _offlineStorage = OfflineStorageService.instance;
  final DataSyncService _syncService = DataSyncService.instance;
  final AuthService _authService = AuthService.instance;

  /// Singleton instance
  static FinancialDataManager get instance {
    _instance ??= FinancialDataManager._internal();
    return _instance!;
  }

  FinancialDataManager._internal() {
    // Listen to connectivity changes
    _connectivity.addListener(_onConnectivityChanged);
  }

  // State variables
  FinancialSummary? _financialSummary;
  List<Transaction> _transactions = [];
  List<Account> _accounts = [];
  List<CategoryModel.Category> _categories = [];
  bool _isLoading = false;
  String? _error;
  DateTime? _lastUpdated;
  bool _isOfflineMode = false;

  // Getters
  FinancialSummary? get financialSummary => _financialSummary;
  List<Transaction> get transactions => _transactions;
  List<Account> get accounts => _accounts;
  List<CategoryModel.Category> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime? get lastUpdated => _lastUpdated;
  bool get isOfflineMode => _isOfflineMode;
  bool get isOnline => _connectivity.isOnline;

  // Computed getters
  double get totalBalance => _financialSummary?.totalBalance ?? 0.0;
  double get monthlyIncome => _financialSummary?.monthlyIncome ?? 0.0;
  double get monthlyExpense => _financialSummary?.monthlyExpense ?? 0.0;
  List<Transaction> get recentTransactions => _transactions.take(10).toList();
  List<CategoryModel.Category> get expenseCategories => _categories.where((c) => c.isExpense).toList();
  List<CategoryModel.Category> get incomeCategories => _categories.where((c) => c.isIncome).toList();
  List<Account> get activeAccounts => _accounts.where((a) => a.isActive).toList();

  /// Get current month name (e.g., "January 2025")
  String get currentMonthLabel {
    final now = DateTime.now();
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[now.month - 1]} ${now.year}';
  }

  /// Calculate previous month income
  double get previousMonthIncome {
    final now = DateTime.now();
    final previousMonth = DateTime(now.year, now.month - 1);
    final currentMonth = DateTime(now.year, now.month);

    double income = 0.0;
    for (final transaction in _transactions) {
      if (transaction.date.isAfter(previousMonth) && transaction.date.isBefore(currentMonth)) {
        if (transaction.type == 'income') {
          income += transaction.amount;
        }
      }
    }
    return income;
  }

  /// Calculate previous month expense
  double get previousMonthExpense {
    final now = DateTime.now();
    final previousMonth = DateTime(now.year, now.month - 1);
    final currentMonth = DateTime(now.year, now.month);

    double expense = 0.0;
    for (final transaction in _transactions) {
      if (transaction.date.isAfter(previousMonth) && transaction.date.isBefore(currentMonth)) {
        if (transaction.type == 'expense') {
          expense += transaction.amount;
        }
      }
    }
    return expense;
  }

  /// Calculate income percentage change (current vs previous month)
  double get incomePercentageChange {
    final current = monthlyIncome;
    final previous = previousMonthIncome;

    if (previous == 0) {
      return current > 0 ? 100.0 : 0.0;
    }

    return ((current - previous) / previous) * 100;
  }

  /// Calculate expense percentage change (current vs previous month)
  double get expensePercentageChange {
    final current = monthlyExpense;
    final previous = previousMonthExpense;

    if (previous == 0) {
      return current > 0 ? 100.0 : 0.0;
    }

    return ((current - previous) / previous) * 100;
  }

  /// Check if we have any data to display (for skeleton loading)
  bool get hasAnyData {
    // For guest users, always show data (even if empty) to avoid infinite skeleton loading
    final authStateManager = AuthStateManager.instance;
    if (authStateManager.isGuestMode) {
      return _financialSummary != null; // Guest users should always have a financial summary
    }

    // For authenticated users, check if we have actual data
    return _transactions.isNotEmpty || _accounts.isNotEmpty || _financialSummary != null;
  }

  /// Handle connectivity changes
  void _onConnectivityChanged() {
    final wasOffline = _isOfflineMode;
    _isOfflineMode = !_connectivity.isOnline;

    if (wasOffline && !_isOfflineMode) {
      // Just came back online, sync data first, then refresh
      debugPrint('📱 Connection restored, syncing offline data...');
      _syncService.syncData().then((_) {
        // After sync is complete, refresh data from server
        debugPrint('📱 Sync complete, refreshing data from server...');
        loadFinancialSummary();
        loadAccounts();
        loadRecentTransactions();
      });
    } else if (!wasOffline && _isOfflineMode) {
      // Just went offline
      debugPrint('📱 Connection lost, switching to offline mode...');
    }

    notifyListeners();
  }

  /// Initialize financial data (works both online and offline)
  Future<void> initialize() async {
    debugPrint('📱 Initializing financial data manager...');

    if (_connectivity.isOnline) {
      // Online mode: Load cached data immediately, then refresh in background
      await _initializeOnlineOptimized();
    } else {
      // Offline mode: Load from local storage
      await _initializeOffline();
    }
  }

  /// Initialize in online mode with optimized loading
  Future<void> _initializeOnlineOptimized() async {
    debugPrint('📱 Initializing in online mode (optimized)...');

    // Step 1: Load cached data immediately for instant display
    await _loadCachedDataForInstantDisplay();

    // Step 2: Load fresh data in parallel in background
    _loadFreshDataInParallel();
  }

  /// Load cached data immediately for instant UI display
  Future<void> _loadCachedDataForInstantDisplay() async {
    debugPrint('📱 Loading cached data for instant display...');

    try {
      final user = await _authService.getCurrentUser();
      if (user != null) {
        // Load all cached data in parallel for instant display
        final futures = await Future.wait([
          _loadCachedTransactions(user.id),
          _loadCachedAccounts(user.id),
          _loadCachedCategories(user.id),
        ]);

        // Calculate summary from cached data
        _calculateOfflineFinancialSummary();

        // Notify listeners immediately with cached data
        notifyListeners();
        debugPrint('📱 Cached data loaded and UI updated instantly');
      }
    } catch (e) {
      debugPrint('Error loading cached data: $e');
      // Continue with fresh data loading even if cache fails
    }
  }

  /// Load fresh data in parallel without blocking UI
  void _loadFreshDataInParallel() {
    debugPrint('📱 Starting parallel fresh data loading...');

    // Load all data in parallel without awaiting
    Future.wait([
      _loadFreshFinancialSummary(),
      _loadFreshAccounts(),
      _loadFreshCategories(),
      _loadFreshRecentTransactions(),
    ]).then((_) {
      debugPrint('📱 All fresh data loaded successfully');
      // Start background sync after fresh data is loaded
      _syncService.syncData();
    }).catchError((error) {
      debugPrint('Error loading fresh data: $error');
      _setError('Failed to refresh data');
    });
  }

  /// Initialize in offline mode
  Future<void> _initializeOffline() async {
    debugPrint('📱 Initializing in offline mode...');
    _isOfflineMode = true;

    final user = await _getCurrentUserSafe();
    if (user != null) {
      await _loadOfflineData(user.id);
    } else {
      // If no user available, create empty state for guest users
      final authStateManager = AuthStateManager.instance;
      if (authStateManager.isGuestMode) {
        _createEmptyFinancialSummary();
        notifyListeners();
      }
    }
  }

  /// Get current user safely (handles both authenticated and guest users)
  Future<User?> _getCurrentUserSafe() async {
    final authStateManager = AuthStateManager.instance;
    if (authStateManager.currentUser != null) {
      return authStateManager.currentUser;
    }
    return await _authService.getCurrentUser();
  }

  /// Load data from offline storage
  Future<void> _loadOfflineData(int userId) async {
    try {
      _setLoading(true);
      _setError(null);

      // Load transactions from local storage
      final transactions = await _offlineStorage.getTransactions(userId);
      _transactions = transactions;

      // Load accounts from local storage
      final accounts = await _offlineStorage.getAccounts(userId);
      _accounts = accounts;

      // Load categories from local storage
      final categories = await _offlineStorage.getCategories(userId);
      _categories = categories;

      // Calculate financial summary from local data
      _calculateOfflineFinancialSummary();

      // For guest users, ensure we always have a financial summary (even if empty)
      // This prevents infinite skeleton loading
      final authStateManager = AuthStateManager.instance;
      if (authStateManager.isGuestMode && _financialSummary == null) {
        _createEmptyFinancialSummary();
      }

      _lastUpdated = DateTime.now();
      notifyListeners();

      debugPrint('✅ Loaded offline data: ${transactions.length} transactions, ${accounts.length} accounts, ${categories.length} categories');
    } catch (e) {
      debugPrint('❌ Error loading offline data: $e');
      _setError('Failed to load offline data');

      // For guest users, create empty financial summary even on error
      final authStateManager = AuthStateManager.instance;
      if (authStateManager.isGuestMode && _financialSummary == null) {
        _createEmptyFinancialSummary();
        notifyListeners();
      }
    } finally {
      _setLoading(false);
    }
  }

  /// Load cached transactions for instant display
  Future<void> _loadCachedTransactions(int userId) async {
    try {
      final cachedTransactions = await _offlineStorage.getTransactions(userId, limit: 10);
      if (cachedTransactions.isNotEmpty) {
        _transactions = cachedTransactions;
        debugPrint('📱 Loaded ${cachedTransactions.length} cached transactions');
      }
    } catch (e) {
      debugPrint('Error loading cached transactions: $e');
    }
  }

  /// Load cached accounts for instant display
  Future<void> _loadCachedAccounts(int userId) async {
    try {
      final cachedAccounts = await _offlineStorage.getAccounts(userId);
      if (cachedAccounts.isNotEmpty) {
        _accounts = cachedAccounts;
        debugPrint('📱 Loaded ${cachedAccounts.length} cached accounts');
      }
    } catch (e) {
      debugPrint('Error loading cached accounts: $e');
    }
  }

  /// Load cached categories for instant display
  Future<void> _loadCachedCategories(int userId) async {
    try {
      final cachedCategories = await _offlineStorage.getCategories(userId);
      if (cachedCategories.isNotEmpty) {
        _categories = cachedCategories;
        debugPrint('📱 Loaded ${cachedCategories.length} cached categories');
      }
    } catch (e) {
      debugPrint('Error loading cached categories: $e');
    }
  }

  /// Update account balance offline when adding transactions
  Future<void> _updateAccountBalanceOffline(String accountName, String transactionType, double amount) async {
    try {
      // Find the account
      final accountIndex = _accounts.indexWhere((account) => account.name == accountName);
      if (accountIndex != -1) {
        final account = _accounts[accountIndex];
        double newBalance = account.balance;

        // Update balance based on transaction type
        if (transactionType == 'income') {
          newBalance += amount;
        } else if (transactionType == 'expense') {
          newBalance -= amount;
        }

        // Create updated account
        final updatedAccount = Account(
          id: account.id,
          userId: account.userId,
          name: account.name,
          type: account.type,
          balance: newBalance,
          currency: account.currency,
          bankName: account.bankName,
          accountNumber: account.accountNumber,
          description: account.description,
          isActive: account.isActive,
          icon: account.icon,
          color: account.color,
          createdAt: account.createdAt,
          updatedAt: DateTime.now(),
        );

        // Update in local list
        _accounts[accountIndex] = updatedAccount;

        // Save to offline storage
        await _offlineStorage.saveAccount(updatedAccount, syncStatus: SyncStatus.pending);

        debugPrint('✅ Account balance updated offline: $accountName = $newBalance');
      }
    } catch (e) {
      debugPrint('❌ Error updating account balance offline: $e');
    }
  }

  /// Calculate financial summary from local data
  void _calculateOfflineFinancialSummary() {
    double totalBalance = 0.0;
    double monthlyIncome = 0.0;
    double monthlyExpense = 0.0;

    // Calculate total balance from accounts
    for (final account in _accounts) {
      if (account.isActive) {
        totalBalance += account.balance;
      }
    }

    // Calculate monthly income and expense from transactions
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month);
    final nextMonth = DateTime(now.year, now.month + 1);

    for (final transaction in _transactions) {
      if (transaction.date.isAfter(currentMonth) && transaction.date.isBefore(nextMonth)) {
        if (transaction.type == 'income') {
          monthlyIncome += transaction.amount;
        } else if (transaction.type == 'expense') {
          monthlyExpense += transaction.amount;
        }
      }
    }

    _financialSummary = FinancialSummary(
      totalBalance: totalBalance,
      totalIncome: monthlyIncome,
      totalExpense: monthlyExpense,
      monthlyIncome: monthlyIncome,
      monthlyExpense: monthlyExpense,
      weeklyIncome: 0.0,
      weeklyExpense: 0.0,
      dailyIncome: 0.0,
      dailyExpense: 0.0,
      accounts: _accounts,
      recentTransactions: _transactions.take(10).toList(),
      categoryExpenses: {},
      categoryIncomes: {},
      monthlyTrends: {},
      lastUpdated: DateTime.now(),
    );

    debugPrint('✅ Financial summary calculated offline');
  }

  /// Create empty financial summary for guest users with no data
  void _createEmptyFinancialSummary() {
    _financialSummary = FinancialSummary(
      totalBalance: 0.0,
      totalIncome: 0.0,
      totalExpense: 0.0,
      monthlyIncome: 0.0,
      monthlyExpense: 0.0,
      weeklyIncome: 0.0,
      weeklyExpense: 0.0,
      dailyIncome: 0.0,
      dailyExpense: 0.0,
      accounts: [],
      recentTransactions: [],
      categoryExpenses: {},
      categoryIncomes: {},
      monthlyTrends: {},
      lastUpdated: DateTime.now(),
    );
    debugPrint('✅ Created empty financial summary for guest user');
  }

  /// Load fresh financial summary from API (non-blocking)
  Future<void> _loadFreshFinancialSummary() async {
    try {
      final response = await _httpClient.get<FinancialSummary>(
        ApiConfig.financialSummaryEndpoint,
        fromJson: (json) => FinancialSummary.fromJson(json),
      );

      if (response.isSuccess && response.data != null) {
        _financialSummary = response.data;
        _lastUpdated = DateTime.now();
        notifyListeners();
        debugPrint('📱 Fresh financial summary loaded');
      }
    } catch (e) {
      debugPrint('Error loading fresh financial summary: $e');
    }
  }

  /// Load fresh accounts from API (non-blocking)
  Future<void> _loadFreshAccounts() async {
    try {
      final response = await _accountService.getAccounts();

      if (response.isSuccess && response.data != null) {
        _accounts = response.data!;

        // Cache accounts locally
        final user = await _authService.getCurrentUser();
        if (user != null) {
          for (final account in _accounts) {
            await _offlineStorage.saveAccount(account, syncStatus: SyncStatus.synced);
          }
        }

        notifyListeners();
        debugPrint('📱 Fresh accounts loaded: ${_accounts.length}');
      }
    } catch (e) {
      debugPrint('Error loading fresh accounts: $e');
    }
  }

  /// Load fresh categories from API (non-blocking)
  Future<void> _loadFreshCategories() async {
    try {
      final response = await _categoryService.getCategories();

      if (response.isSuccess && response.data != null) {
        _categories = response.data!;

        // Cache categories locally
        final user = await _authService.getCurrentUser();
        if (user != null) {
          for (final category in _categories) {
            await _offlineStorage.saveCategory(category, syncStatus: SyncStatus.synced);
          }
        }

        notifyListeners();
        debugPrint('📱 Fresh categories loaded: ${_categories.length}');
      }
    } catch (e) {
      debugPrint('Error loading fresh categories: $e');
    }
  }

  /// Load fresh recent transactions from API (non-blocking)
  Future<void> _loadFreshRecentTransactions() async {
    try {
      final response = await _transactionService.getRecentTransactions();

      if (response.isSuccess && response.data != null) {
        _transactions = response.data!;

        // Cache transactions locally
        final user = await _authService.getCurrentUser();
        if (user != null) {
          for (final transaction in _transactions) {
            await _offlineStorage.saveTransaction(transaction, syncStatus: SyncStatus.synced);
          }
        }

        notifyListeners();
        debugPrint('📱 Fresh recent transactions loaded: ${_transactions.length}');
      }
    } catch (e) {
      debugPrint('Error loading fresh recent transactions: $e');
    }
  }

  /// Load financial summary from API
  Future<void> loadFinancialSummary() async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await _httpClient.get<FinancialSummary>(
        ApiConfig.financialSummaryEndpoint,
        fromJson: (json) => FinancialSummary.fromJson(json),
      );

      if (response.isSuccess && response.data != null) {
        _financialSummary = response.data;
        _lastUpdated = DateTime.now();
        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load financial summary');
      }
    } catch (e) {
      debugPrint('Load financial summary error: $e');
      _setError('Failed to load financial summary');
    } finally {
      _setLoading(false);
    }
  }

  /// Load all accounts (with offline caching)
  Future<void> loadAccounts() async {
    try {
      final user = await _getCurrentUserSafe();
      if (user == null) {
        _setError('No user available');
        return;
      }

      if (_connectivity.isOnline && user.id != -1) {
        // Online mode for authenticated users
        final response = await _accountService.getAccounts();

        if (response.isSuccess && response.data != null) {
          _accounts = response.data!;

          // Cache accounts locally
          for (final account in _accounts) {
            await _offlineStorage.saveAccount(account, syncStatus: SyncStatus.synced);
          }

          notifyListeners();
        } else {
          _setError(response.message ?? 'Failed to load accounts');
        }
      } else {
        // Load from offline storage (for both offline mode and guest users)
        _accounts = await _offlineStorage.getAccounts(user.id);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Load accounts error: $e');
      _setError('Failed to load accounts');
    }
  }

  /// Load all categories (with offline caching)
  Future<void> loadCategories() async {
    try {
      if (_connectivity.isOnline) {
        final response = await _categoryService.getCategories();

        if (response.isSuccess && response.data != null) {
          _categories = response.data!;

          // Cache categories locally
          final user = await _authService.getCurrentUser();
          if (user != null) {
            for (final category in _categories) {
              await _offlineStorage.saveCategory(category, syncStatus: SyncStatus.synced);
            }
          }

          notifyListeners();
        } else {
          _setError(response.message ?? 'Failed to load categories');
        }
      } else {
        // Load from offline storage
        final user = await _authService.getCurrentUser();
        if (user != null) {
          _categories = await _offlineStorage.getCategories(user.id);
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Load categories error: $e');
      _setError('Failed to load categories');
    }
  }

  /// Load recent transactions (with offline caching)
  Future<void> loadRecentTransactions() async {
    try {
      if (_connectivity.isOnline) {
        final response = await _transactionService.getRecentTransactions();

        if (response.isSuccess && response.data != null) {
          _transactions = response.data!;

          // Cache transactions locally
          final user = await _getCurrentUserSafe();
          if (user != null) {
            for (final transaction in _transactions) {
              await _offlineStorage.saveTransaction(transaction, syncStatus: SyncStatus.synced);
            }
          }

          notifyListeners();
        } else {
          _setError(response.message ?? 'Failed to load transactions');
        }
      } else {
        // Load from offline storage
        final user = await _getCurrentUserSafe();
        if (user != null) {
          _transactions = await _offlineStorage.getTransactions(user.id, limit: 10);
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Load transactions error: $e');
      _setError('Failed to load transactions');
    }
  }

  /// Load all transactions
  Future<void> loadAllTransactions({
    int? limit,
    int? offset,
    String? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _setLoading(true);
      
      final response = await _transactionService.getTransactions(
        limit: limit,
        offset: offset,
        type: type,
        startDate: startDate,
        endDate: endDate,
      );
      
      if (response.isSuccess && response.data != null) {
        _transactions = response.data!;
        notifyListeners();
      } else {
        _setError(response.message ?? 'Failed to load transactions');
      }
    } catch (e) {
      debugPrint('Load all transactions error: $e');
      _setError('Failed to load transactions');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new transaction (works offline)
  Future<bool> addTransaction({
    required String type,
    required double amount,
    required String category,
    String? subcategory,
    required String account,
    String? description,
    String? notes,
    required DateTime date,
    bool isRecurring = false,
    String? recurringFrequency,
    DateTime? recurringEndDate,
    String? tags,
    String? location,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final user = await _getCurrentUserSafe();
      if (user == null) {
        _setError('No user available');
        return false;
      }

      if (_connectivity.isOnline && user.id != -1) {
        // Online mode: Create via API
        final response = await _transactionService.createTransaction(
          type: type,
          amount: amount,
          category: category,
          subcategory: subcategory,
          account: account,
          description: description,
          notes: notes,
          date: date,
          isRecurring: isRecurring,
          recurringFrequency: recurringFrequency,
          recurringEndDate: recurringEndDate,
          tags: tags,
          location: location,
        );

        if (response.isSuccess && response.data != null) {
          // Add to local list and cache
          _transactions.insert(0, response.data!);
          await _offlineStorage.saveTransaction(response.data!, syncStatus: SyncStatus.synced);

          // Refresh financial summary and accounts
          await loadFinancialSummary();
          await loadAccounts();

          notifyListeners();
          return true;
        } else {
          _setError(response.message ?? 'Failed to add transaction');
          return false;
        }
      } else {
        // Offline mode: Create locally and queue for sync
        // Generate a temporary negative ID for offline transactions
        final tempId = -(DateTime.now().millisecondsSinceEpoch);

        final transaction = Transaction(
          id: tempId,
          userId: user.id,
          type: type,
          amount: amount,
          category: category,
          subcategory: subcategory,
          account: account,
          description: description,
          notes: notes,
          date: date,
          isRecurring: isRecurring,
          recurringFrequency: recurringFrequency,
          recurringEndDate: recurringEndDate,
          tags: tags,
          location: location,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Save locally with pending sync status
        await _offlineStorage.saveTransaction(transaction, syncStatus: SyncStatus.pending);

        // Add to sync queue
        await _syncService.queueTransactionSync(transaction, SyncOperation.create);

        // Add to local list
        _transactions.insert(0, transaction);

        // Update account balance locally
        await _updateAccountBalanceOffline(transaction.account, transaction.type, transaction.amount);

        // Recalculate financial summary
        _calculateOfflineFinancialSummary();

        notifyListeners();

        debugPrint('✅ Transaction created offline with temp ID: $tempId');
        return true;
      }
    } catch (e) {
      debugPrint('Add transaction error: $e');
      _setError('Failed to add transaction');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update a transaction
  Future<bool> updateTransaction({
    required int transactionId,
    String? type,
    double? amount,
    String? category,
    String? subcategory,
    String? account,
    String? description,
    String? notes,
    DateTime? date,
    bool? isRecurring,
    String? recurringFrequency,
    DateTime? recurringEndDate,
    String? tags,
    String? location,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await _transactionService.updateTransaction(
        transactionId: transactionId,
        type: type,
        amount: amount,
        category: category,
        subcategory: subcategory,
        account: account,
        description: description,
        notes: notes,
        date: date,
        isRecurring: isRecurring,
        recurringFrequency: recurringFrequency,
        recurringEndDate: recurringEndDate,
        tags: tags,
        location: location,
      );

      if (response.isSuccess && response.data != null) {
        // Update local list
        final index = _transactions.indexWhere((t) => t.id == transactionId);
        if (index != -1) {
          _transactions[index] = response.data!;
        }
        
        // Refresh financial summary and accounts
        await loadFinancialSummary();
        await loadAccounts();
        
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to update transaction');
        return false;
      }
    } catch (e) {
      debugPrint('Update transaction error: $e');
      _setError('Failed to update transaction');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a transaction
  Future<bool> deleteTransaction(int transactionId) async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await _transactionService.deleteTransaction(transactionId);

      if (response.isSuccess) {
        // Remove from local list
        _transactions.removeWhere((t) => t.id == transactionId);
        
        // Refresh financial summary and accounts
        await loadFinancialSummary();
        await loadAccounts();
        
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to delete transaction');
        return false;
      }
    } catch (e) {
      debugPrint('Delete transaction error: $e');
      _setError('Failed to delete transaction');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Add new account
  Future<bool> addAccount({
    required String name,
    required String accountType,
    double balance = 0.0,
    String? bankName,
    String? accountNumber,
    String accountIcon = 'account_balance_wallet',
    String accountColor = '#2196F3',
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final user = await _getCurrentUserSafe();
      if (user == null) {
        _setError('No user available');
        return false;
      }

      if (_connectivity.isOnline && user.id != -1) {
        // Online mode for authenticated users
        final response = await _accountService.createAccount(
          name: name,
          type: accountType,
          initialBalance: balance,
          bankName: bankName,
          accountNumber: accountNumber,
          icon: accountIcon,
          color: accountColor,
        );

        if (response.isSuccess) {
          // Refresh accounts and financial summary
          await loadAccounts();
          await loadFinancialSummary();
          return true;
        } else {
          _setError(response.message ?? 'Failed to create account');
          return false;
        }
      } else {
        // Offline mode or guest user - create account locally
        final account = Account(
          id: -(DateTime.now().millisecondsSinceEpoch), // Temporary negative ID
          userId: user.id,
          name: name,
          type: accountType,
          balance: balance,
          bankName: bankName,
          accountNumber: accountNumber,
          icon: accountIcon,
          color: accountColor,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Save locally
        await _offlineStorage.saveAccount(account, syncStatus: SyncStatus.pending);

        // Add to local list
        _accounts.add(account);
        notifyListeners();

        debugPrint('✅ Account created locally: ${account.name}');
        return true;
      }
    } catch (e) {
      debugPrint('Add account error: $e');
      _setError('Failed to create account');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update existing account
  Future<bool> updateAccount({
    required int accountId,
    String? name,
    String? accountType,
    double? balance,
    String? bankName,
    String? accountNumber,
    String? accountIcon,
    String? accountColor,
    bool? isActive,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await _accountService.updateAccount(
        accountId: accountId,
        name: name,
        type: accountType,
        balance: balance,
        bankName: bankName,
        accountNumber: accountNumber,
        icon: accountIcon,
        color: accountColor,
        isActive: isActive,
      );

      if (response.isSuccess) {
        // Refresh accounts and financial summary
        await loadAccounts();
        await loadFinancialSummary();
        return true;
      } else {
        _setError(response.message ?? 'Failed to update account');
        return false;
      }
    } catch (e) {
      debugPrint('Update account error: $e');
      _setError('Failed to update account');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete account
  Future<bool> deleteAccount(int accountId) async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await _accountService.deleteAccount(accountId);

      if (response.isSuccess) {
        // Remove from local list
        _accounts.removeWhere((account) => account.id == accountId);

        // Refresh financial summary
        await loadFinancialSummary();

        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to delete account');
        return false;
      }
    } catch (e) {
      debugPrint('Delete account error: $e');
      _setError('Failed to delete account');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh accounts only
  Future<void> refreshAccounts() async {
    await loadAccounts();
  }

  /// Refresh all data
  Future<void> refreshAll() async {
    await initialize();
  }

  /// Clear all data (for logout)
  void clearData() {
    _financialSummary = null;
    _transactions.clear();
    _accounts.clear();
    _categories.clear();
    _error = null;
    _lastUpdated = null;
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error state
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// Get account by name
  Account? getAccountByName(String name) {
    try {
      return _accounts.firstWhere((account) => account.name == name);
    } catch (e) {
      return null;
    }
  }

  /// Get category by name
  CategoryModel.Category? getCategoryByName(String name) {
    try {
      return _categories.firstWhere((category) => category.name == name);
    } catch (e) {
      return null;
    }
  }
}
