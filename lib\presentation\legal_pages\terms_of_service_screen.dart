import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../core/app_export.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppTheme.primaryTeal,
            size: 6.w,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Terms of Service',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppTheme.primaryTeal,
                fontWeight: FontWeight.w600,
              ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: AppTheme.primaryTeal.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.primaryTeal.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.description,
                    color: AppTheme.primaryTeal,
                    size: 8.w,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'FinTrack Terms of Service',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppTheme.primaryTeal,
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Last updated: ${DateTime.now().year}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.neutralGray,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            SizedBox(height: 4.h),

            // Terms Content
            _buildSection(
              context,
              '1. Acceptance of Terms',
              'By downloading, installing, or using the FinTrack mobile application, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our app.',
            ),

            _buildSection(
              context,
              '2. Description of Service',
              'FinTrack is a personal finance management application that helps you track your income, expenses, and financial goals. The app provides tools for budgeting, expense categorization, and financial reporting.',
            ),

            _buildSection(
              context,
              '3. User Accounts',
              'To use certain features of FinTrack, you must create an account. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.',
            ),

            _buildSection(
              context,
              '4. Privacy and Data',
              'Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your personal information. By using FinTrack, you consent to our data practices as described in our Privacy Policy.',
            ),

            _buildSection(
              context,
              '5. User Responsibilities',
              'You agree to:\n• Provide accurate and complete information\n• Use the app only for lawful purposes\n• Not attempt to reverse engineer or modify the app\n• Not share your account credentials with others\n• Keep your financial data secure',
            ),

            _buildSection(
              context,
              '6. Intellectual Property',
              'FinTrack and all its content, features, and functionality are owned by us and are protected by copyright, trademark, and other intellectual property laws.',
            ),

            _buildSection(
              context,
              '7. Limitation of Liability',
              'FinTrack is provided "as is" without warranties of any kind. We are not liable for any damages arising from your use of the app, including but not limited to financial losses or data loss.',
            ),

            _buildSection(
              context,
              '8. Changes to Terms',
              'We reserve the right to modify these terms at any time. We will notify users of significant changes through the app or via email. Continued use of the app after changes constitutes acceptance of the new terms.',
            ),

            _buildSection(
              context,
              '9. Termination',
              'We may terminate or suspend your account at any time for violation of these terms. You may also delete your account at any time through the app settings.',
            ),

            _buildSection(
              context,
              '10. Contact Information',
              'If you have any questions about these Terms of Service, please contact us through the app\'s support feature or at our official support channels.',
            ),

            SizedBox(height: 4.h),

            // Footer
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: AppTheme.neutralGray.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'By continuing to use FinTrack, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.neutralGray,
                      fontStyle: FontStyle.italic,
                    ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: EdgeInsets.only(bottom: 3.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppTheme.primaryTeal,
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 1.h),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.neutralGray,
                  height: 1.5,
                ),
          ),
        ],
      ),
    );
  }
}
