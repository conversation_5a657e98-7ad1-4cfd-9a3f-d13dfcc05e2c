import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class TransactionCardWidget extends StatelessWidget {
  final Transaction transaction;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool isSelected;
  final VoidCallback? onLongPress;
  final VoidCallback? onTap;

  const TransactionCardWidget({
    super.key,
    required this.transaction,
    this.onEdit,
    this.onDelete,
    this.isSelected = false,
    this.onLongPress,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDark = Theme.of(context).brightness == Brightness.dark;
    final bool isIncome = transaction.isIncome;

    return ListenableBuilder(
      listenable: FormattingService.instance,
      builder: (context, child) {
        final String amount = transaction.formattedAmount;
        final String description = transaction.displayDescription;
        final String category = transaction.category;
        final String account = transaction.account;
        final DateTime timestamp = transaction.date;
        final String categoryIcon = _getCategoryIcon(category);

        return Dismissible(
      key: Key(transaction.id.toString()),
      background: Container(
        margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
        decoration: BoxDecoration(
          color: AppTheme.primaryTeal,
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.only(left: 6.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'edit',
              color: Colors.white,
              size: 6.w,
            ),
            SizedBox(height: 0.5.h),
            Text(
              'Edit',
              style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
      secondaryBackground: Container(
        margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
        decoration: BoxDecoration(
          color: AppTheme.alertRed,
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 6.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'delete',
              color: Colors.white,
              size: 6.w,
            ),
            SizedBox(height: 0.5.h),
            Text(
              'Delete',
              style: AppTheme.lightTheme.textTheme.labelSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          onEdit?.call();
          return false;
        } else if (direction == DismissDirection.endToStart) {
          return await _showDeleteConfirmation(context);
        }
        return false;
      },
      child: GestureDetector(
        onLongPress: onLongPress,
        onTap: onTap,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: isSelected
                ? (isDark
                    ? AppTheme.primaryTeal.withValues(alpha: 0.2)
                    : AppTheme.primaryTeal.withValues(alpha: 0.1))
                : (isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface),
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(color: AppTheme.primaryTeal, width: 2)
                : Border.all(
                    color: isDark
                        ? AppTheme.neutralGray.withValues(alpha: 0.2)
                        : AppTheme.borderSubtle,
                    width: 1),
            boxShadow: [
              BoxShadow(
                color: isDark ? AppTheme.shadowDark : AppTheme.shadowLight,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(4.w),
            child: Row(
              children: [
                // Selection checkbox (visible only in multi-select mode)
                if (isSelected) ...[
                  Container(
                    width: 6.w,
                    height: 6.w,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryTeal,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: CustomIconWidget(
                      iconName: 'check',
                      color: Colors.white,
                      size: 4.w,
                    ),
                  ),
                  SizedBox(width: 3.w),
                ],

                // Category icon
                Container(
                  width: 12.w,
                  height: 12.w,
                  decoration: BoxDecoration(
                    color: isIncome
                        ? AppTheme.successGreen.withValues(alpha: 0.1)
                        : AppTheme.alertRed.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: CustomIconWidget(
                      iconName: categoryIcon,
                      color:
                          isIncome ? AppTheme.successGreen : AppTheme.alertRed,
                      size: 6.w,
                    ),
                  ),
                ),

                SizedBox(width: 4.w),

                // Transaction details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              description,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: isDark
                                        ? AppTheme.textHighEmphasisDark
                                        : AppTheme.textHighEmphasisLight,
                                  ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            amount,
                            style: AppTheme.financialAmountStyle(
                              isLight: !isDark,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ).copyWith(
                              color: isIncome
                                  ? AppTheme.successGreen
                                  : AppTheme.alertRed,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 1.h),
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                CustomIconWidget(
                                  iconName: 'category',
                                  color: isDark
                                      ? AppTheme.textMediumEmphasisDark
                                      : AppTheme.textMediumEmphasisLight,
                                  size: 3.5.w,
                                ),
                                SizedBox(width: 1.w),
                                Flexible(
                                  child: Text(
                                    category,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: isDark
                                              ? AppTheme.textMediumEmphasisDark
                                              : AppTheme
                                                  .textMediumEmphasisLight,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 2.w),
                          Row(
                            children: [
                              CustomIconWidget(
                                iconName: 'account_balance_wallet',
                                color: isDark
                                    ? AppTheme.textMediumEmphasisDark
                                    : AppTheme.textMediumEmphasisLight,
                                size: 3.5.w,
                              ),
                              SizedBox(width: 1.w),
                              Text(
                                account,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: isDark
                                          ? AppTheme.textMediumEmphasisDark
                                          : AppTheme.textMediumEmphasisLight,
                                    ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        _formatTimestamp(timestamp),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: isDark
                                  ? AppTheme.textDisabledDark
                                  : AppTheme.textDisabledLight,
                              fontSize: 10.sp,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
        );
      },
    );
  }

  Future<bool> _showDeleteConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                'Delete Transaction',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              content: Text(
                'Are you sure you want to delete this transaction? This action cannot be undone.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: AppTheme.neutralGray),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                    onDelete?.call();
                  },
                  child: Text(
                    'Delete',
                    style: TextStyle(color: AppTheme.alertRed),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes}m ago';
      }
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return FormattingService.instance.formatDate(timestamp);
    }
  }

  /// Get category icon based on category name
  String _getCategoryIcon(String category) {
    final categoryName = category.toLowerCase();

    // Income categories
    if (transaction.isIncome) {
      if (categoryName.contains('salary')) return 'work';
      if (categoryName.contains('business')) return 'business';
      if (categoryName.contains('investment')) return 'trending_up';
      if (categoryName.contains('freelance')) return 'laptop';
      return 'attach_money';
    }

    // Expense categories
    if (categoryName.contains('food') || categoryName.contains('dining')) return 'restaurant';
    if (categoryName.contains('transport') || categoryName.contains('fuel')) return 'directions_car';
    if (categoryName.contains('shopping') || categoryName.contains('groceries')) return 'shopping_cart';
    if (categoryName.contains('entertainment') || categoryName.contains('movie')) return 'movie';
    if (categoryName.contains('health') || categoryName.contains('medical')) return 'local_hospital';
    if (categoryName.contains('education')) return 'school';
    if (categoryName.contains('bill') || categoryName.contains('utility')) return 'receipt';
    if (categoryName.contains('gas') || categoryName.contains('fuel')) return 'local_gas_station';

    return 'category';
  }
}
