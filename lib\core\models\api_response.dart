import 'user_model.dart';

/// Generic API response wrapper
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final String? error;
  final int? statusCode;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.error,
    this.statusCode,
  });

  /// Getter for backward compatibility
  bool get isSuccess => success;

  /// Create ApiResponse from JSON
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'],
      error: json['error'],
      statusCode: json['status_code'],
    );
  }

  /// Create success response
  factory ApiResponse.success({
    required String message,
    T? data,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: true,
      message: message,
      data: data,
      statusCode: statusCode,
    );
  }

  /// Create error response
  factory ApiResponse.error({
    required String message,
    String? error,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      error: error,
      statusCode: statusCode,
    );
  }

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, data: $data, error: $error, statusCode: $statusCode)';
  }
}

/// Authentication response containing user data and token
class AuthResponse {
  final User user;
  final String token;
  final String tokenType;

  const AuthResponse({
    required this.user,
    required this.token,
    required this.tokenType,
  });

  /// Create AuthResponse from JSON
  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      user: User.fromJson(json['user']),
      token: json['token'] ?? '',
      tokenType: json['token_type'] ?? 'Bearer',
    );
  }

  /// Convert AuthResponse to JSON
  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'token': token,
      'token_type': tokenType,
    };
  }

  @override
  String toString() {
    return 'AuthResponse(user: $user, token: $token, tokenType: $tokenType)';
  }
}

/// API Error details
class ApiError {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final int? statusCode;

  const ApiError({
    required this.message,
    this.code,
    this.details,
    this.statusCode,
  });

  /// Create ApiError from JSON
  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      message: json['message'] ?? 'Unknown error occurred',
      code: json['code'],
      details: json['details'],
      statusCode: json['status_code'],
    );
  }

  /// Create ApiError from exception
  factory ApiError.fromException(Exception exception, {int? statusCode}) {
    return ApiError(
      message: exception.toString(),
      statusCode: statusCode,
    );
  }

  /// Create network error
  factory ApiError.networkError() {
    return const ApiError(
      message: 'Network error. Please check your internet connection.',
      code: 'NETWORK_ERROR',
    );
  }

  /// Create timeout error
  factory ApiError.timeoutError() {
    return const ApiError(
      message: 'Request timeout. Please try again.',
      code: 'TIMEOUT_ERROR',
    );
  }

  /// Create server error
  factory ApiError.serverError({int? statusCode}) {
    return ApiError(
      message: 'Server error. Please try again later.',
      code: 'SERVER_ERROR',
      statusCode: statusCode,
    );
  }

  /// Create unauthorized error
  factory ApiError.unauthorized() {
    return const ApiError(
      message: 'Unauthorized. Please login again.',
      code: 'UNAUTHORIZED',
      statusCode: 401,
    );
  }

  /// Create validation error
  factory ApiError.validationError(Map<String, dynamic> errors) {
    return ApiError(
      message: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: errors,
      statusCode: 422,
    );
  }

  @override
  String toString() {
    return 'ApiError(message: $message, code: $code, details: $details, statusCode: $statusCode)';
  }
}

/// Test API response
class TestResponse {
  final bool success;
  final String message;
  final String timestamp;

  const TestResponse({
    required this.success,
    required this.message,
    required this.timestamp,
  });

  /// Create TestResponse from JSON
  factory TestResponse.fromJson(Map<String, dynamic> json) {
    return TestResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      timestamp: json['timestamp'] ?? '',
    );
  }

  @override
  String toString() {
    return 'TestResponse(success: $success, message: $message, timestamp: $timestamp)';
  }
}
