import 'dart:convert';
import '../services/formatting_service.dart';

/// Transaction model representing a financial transaction
class Transaction {
  final int? id;
  final int userId;
  final String type; // 'income' or 'expense'
  final double amount;
  final String category;
  final String? subcategory;
  final String account;
  final String? description;
  final String? notes;
  final DateTime date;
  final bool isRecurring;
  final String? recurringFrequency;
  final DateTime? recurringEndDate;
  final String? tags;
  final String? location;
  final String? receipt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Transaction({
    this.id,
    required this.userId,
    required this.type,
    required this.amount,
    required this.category,
    this.subcategory,
    required this.account,
    this.description,
    this.notes,
    required this.date,
    this.isRecurring = false,
    this.recurringFrequency,
    this.recurringEndDate,
    this.tags,
    this.location,
    this.receipt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Transaction from JSON
  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] is String ? int.parse(json['id']) : json['id'],
      userId: json['user_id'] is String ? int.parse(json['user_id']) : json['user_id'],
      type: json['type'] ?? '',
      amount: (json['amount'] is String) 
          ? double.parse(json['amount']) 
          : (json['amount'] as num).toDouble(),
      category: json['category'] ?? '',
      subcategory: json['subcategory'],
      account: json['account'] ?? '',
      description: json['description'],
      notes: json['notes'],
      date: DateTime.parse(json['date']),
      isRecurring: json['is_recurring'] == 1 || json['is_recurring'] == true,
      recurringFrequency: json['recurring_frequency'],
      recurringEndDate: json['recurring_end_date'] != null
          ? DateTime.parse(json['recurring_end_date'])
          : null,
      tags: json['tags'],
      location: json['location'],
      receipt: json['receipt'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  /// Convert Transaction to JSON
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'user_id': userId,
      'type': type,
      'amount': amount,
      'category': category,
      if (subcategory != null) 'subcategory': subcategory,
      'account': account,
      if (description != null) 'description': description,
      if (notes != null) 'notes': notes,
      'date': date.toIso8601String(),
      'is_recurring': isRecurring ? 1 : 0,
      if (recurringFrequency != null) 'recurring_frequency': recurringFrequency,
      if (recurringEndDate != null) 'recurring_end_date': recurringEndDate!.toIso8601String(),
      if (tags != null) 'tags': tags,
      if (location != null) 'location': location,
      if (receipt != null) 'receipt': receipt,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Convert Transaction to JSON string
  String toJsonString() {
    return json.encode(toJson());
  }

  /// Create Transaction from JSON string
  factory Transaction.fromJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return Transaction.fromJson(json);
  }

  /// Create a copy of Transaction with updated fields
  Transaction copyWith({
    int? id,
    int? userId,
    String? type,
    double? amount,
    String? category,
    String? subcategory,
    String? account,
    String? description,
    String? notes,
    DateTime? date,
    bool? isRecurring,
    String? recurringFrequency,
    DateTime? recurringEndDate,
    String? tags,
    String? location,
    String? receipt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      account: account ?? this.account,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      date: date ?? this.date,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringFrequency: recurringFrequency ?? this.recurringFrequency,
      recurringEndDate: recurringEndDate ?? this.recurringEndDate,
      tags: tags ?? this.tags,
      location: location ?? this.location,
      receipt: receipt ?? this.receipt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if transaction is income
  bool get isIncome => type.toLowerCase() == 'income';

  /// Check if transaction is expense
  bool get isExpense => type.toLowerCase() == 'expense';

  /// Get formatted amount with currency
  String get formattedAmount {
    return FormattingService.instance.formatCurrency(amount);
  }

  /// Get formatted date
  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get formatted time
  String get formattedTime {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Get display description
  String get displayDescription {
    return description?.isNotEmpty == true ? description! : category;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Transaction &&
        other.id == id &&
        other.userId == userId &&
        other.type == type &&
        other.amount == amount &&
        other.category == category &&
        other.account == account &&
        other.date == date;
  }

  @override
  int get hashCode {
    return Object.hash(id, userId, type, amount, category, account, date);
  }

  @override
  String toString() {
    return 'Transaction(id: $id, userId: $userId, type: $type, amount: $amount, category: $category, account: $account, date: $date)';
  }
}
