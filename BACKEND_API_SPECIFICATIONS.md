# FinTrack Backend API Specifications

## Overview
This document outlines the complete backend API specifications for the FinTrack personal finance application. The backend should be implemented in PHP and deployed at `https://expenseflow.incrediblemarathi.com/api/`.

## Database Schema

### 1. Users Table (Already Exists)
```sql
-- This table already exists from the authentication system
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    is_email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. Accounts Table
```sql
CREATE TABLE accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VARCHA<PERSON>(255) NOT NULL,
    type ENUM('cash', 'bank', 'upi', 'credit_card', 'digital_wallet') NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'INR',
    bank_name VARCHAR(255) NULL,
    account_number VARCHAR(255) NULL,
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    icon VARCHAR(100) NULL,
    color VARCHAR(7) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_accounts (user_id, is_active)
);
```

### 3. Categories Table
```sql
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL, -- NULL for system categories, user_id for custom categories
    name VARCHAR(255) NOT NULL,
    type ENUM('income', 'expense') NOT NULL,
    parent_category VARCHAR(255) NULL,
    description TEXT NULL,
    icon VARCHAR(100) NULL,
    color VARCHAR(7) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_system BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_categories (user_id, type, is_active),
    INDEX idx_system_categories (is_system, type, is_active)
);
```

### 4. Transactions Table
```sql
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type ENUM('income', 'expense') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    category VARCHAR(255) NOT NULL,
    subcategory VARCHAR(255) NULL,
    account VARCHAR(255) NOT NULL,
    description TEXT NULL,
    notes TEXT NULL,
    date DATETIME NOT NULL,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurring_frequency ENUM('daily', 'weekly', 'monthly', 'yearly') NULL,
    recurring_end_date DATETIME NULL,
    tags TEXT NULL,
    location VARCHAR(255) NULL,
    receipt VARCHAR(500) NULL, -- URL to receipt image
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_transactions (user_id, date DESC),
    INDEX idx_transaction_type (user_id, type, date DESC),
    INDEX idx_transaction_category (user_id, category, date DESC),
    INDEX idx_transaction_account (user_id, account, date DESC)
);
```

## API Endpoints

### Authentication Endpoints (Already Implemented)
- `POST /register` - User registration
- `POST /login` - User login
- `POST /logout` - User logout
- `GET /profile` - Get user profile
- `PUT /profile` - Update user profile

### Account Management Endpoints

#### GET /accounts
Get all accounts for the authenticated user.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `active` (optional): Filter by active status (1 for active only)
- `type` (optional): Filter by account type

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "user_id": 1,
            "name": "Main Bank Account",
            "type": "bank",
            "balance": "125000.00",
            "currency": "INR",
            "bank_name": "State Bank of India",
            "account_number": "****1234",
            "description": "Primary savings account",
            "is_active": true,
            "icon": "account_balance",
            "color": "#9C27B0",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]
}
```

#### POST /accounts
Create a new account.

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "name": "Cash Wallet",
    "type": "cash",
    "balance": 5000.00,
    "currency": "INR",
    "bank_name": null,
    "account_number": null,
    "description": "Daily cash expenses",
    "icon": "money",
    "color": "#4CAF50"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 2,
        "user_id": 1,
        "name": "Cash Wallet",
        "type": "cash",
        "balance": "5000.00",
        "currency": "INR",
        "bank_name": null,
        "account_number": null,
        "description": "Daily cash expenses",
        "is_active": true,
        "icon": "money",
        "color": "#4CAF50",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
}
```

#### GET /accounts/{id}
Get a specific account by ID.

#### PUT /accounts/{id}
Update an existing account.

#### DELETE /accounts/{id}
Delete an account (soft delete - set is_active to false).

#### GET /accounts/total-balance
Get total balance across all active accounts.

**Response:**
```json
{
    "success": true,
    "data": {
        "total_balance": "130000.00"
    }
}
```

#### POST /accounts/transfer
Transfer money between accounts.

**Request Body:**
```json
{
    "from_account_id": 1,
    "to_account_id": 2,
    "amount": 1000.00,
    "description": "Transfer to cash wallet"
}
```

### Category Management Endpoints

#### GET /categories
Get all categories (system + user categories).

**Query Parameters:**
- `type` (optional): Filter by type ('income' or 'expense')
- `system` (optional): Get only system categories (1)
- `user` (optional): Get only user categories (1)

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "user_id": null,
            "name": "Food & Dining",
            "type": "expense",
            "parent_category": null,
            "description": "Restaurant meals and food delivery",
            "icon": "restaurant",
            "color": "#FF9800",
            "is_active": true,
            "is_system": true,
            "sort_order": 1,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]
}
```

#### POST /categories
Create a custom category.

#### PUT /categories/{id}
Update a custom category (only user-created categories).

#### DELETE /categories/{id}
Delete a custom category.

#### POST /categories/initialize-defaults
Initialize default system categories for a new user.

#### GET /categories/stats
Get category usage statistics.

**Query Parameters:**
- `start_date` (optional): Start date for statistics
- `end_date` (optional): End date for statistics

### Transaction Management Endpoints

#### GET /transactions
Get all transactions for the authenticated user.

**Query Parameters:**
- `limit` (optional): Number of transactions to return (default: 50)
- `offset` (optional): Offset for pagination (default: 0)
- `type` (optional): Filter by type ('income' or 'expense')
- `category` (optional): Filter by category
- `account` (optional): Filter by account
- `start_date` (optional): Start date filter (ISO 8601)
- `end_date` (optional): End date filter (ISO 8601)

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "user_id": 1,
            "type": "expense",
            "amount": "2450.00",
            "category": "Food & Dining",
            "subcategory": null,
            "account": "UPI",
            "description": "Lunch at restaurant",
            "notes": "Team lunch",
            "date": "2024-01-01T12:30:00Z",
            "is_recurring": false,
            "recurring_frequency": null,
            "recurring_end_date": null,
            "tags": "food,team",
            "location": "Mumbai",
            "receipt": null,
            "created_at": "2024-01-01T12:30:00Z",
            "updated_at": "2024-01-01T12:30:00Z"
        }
    ],
    "pagination": {
        "total": 150,
        "limit": 50,
        "offset": 0,
        "has_more": true
    }
}
```

#### POST /transactions
Create a new transaction.

**Request Body:**
```json
{
    "type": "expense",
    "amount": 1500.00,
    "category": "Transportation",
    "subcategory": "Fuel",
    "account": "Cash",
    "description": "Petrol for car",
    "notes": "Monthly fuel expense",
    "date": "2024-01-01T10:00:00Z",
    "is_recurring": false,
    "tags": "fuel,car",
    "location": "Pune"
}
```

#### GET /transactions/{id}
Get a specific transaction by ID.

#### PUT /transactions/{id}
Update an existing transaction.

#### DELETE /transactions/{id}
Delete a transaction.

#### GET /transactions/search
Search transactions.

**Query Parameters:**
- `search` (required): Search query
- `type` (optional): Filter by type
- `min_amount` (optional): Minimum amount filter
- `max_amount` (optional): Maximum amount filter
- `start_date` (optional): Start date filter
- `end_date` (optional): End date filter

### Financial Summary Endpoints

#### GET /financial-summary
Get comprehensive financial summary for the authenticated user.

**Response:**
```json
{
    "success": true,
    "data": {
        "total_balance": "125000.00",
        "total_income": "55000.00",
        "total_expense": "32000.00",
        "monthly_income": "45000.00",
        "monthly_expense": "28000.00",
        "weekly_income": "11250.00",
        "weekly_expense": "7000.00",
        "daily_income": "1607.14",
        "daily_expense": "1000.00",
        "accounts": [
            {
                "id": 1,
                "name": "Main Bank Account",
                "type": "bank",
                "balance": "120000.00"
            }
        ],
        "recent_transactions": [
            {
                "id": 1,
                "type": "expense",
                "amount": "2450.00",
                "category": "Food & Dining",
                "description": "Lunch at restaurant",
                "date": "2024-01-01T12:30:00Z"
            }
        ],
        "category_expenses": {
            "Food & Dining": "8500.00",
            "Transportation": "5200.00",
            "Shopping": "3800.00"
        },
        "category_incomes": {
            "Salary": "45000.00",
            "Freelance": "8000.00",
            "Investment": "2000.00"
        },
        "monthly_trends": {
            "2024-01": "17000.00",
            "2024-02": "23000.00",
            "2024-03": "28000.00"
        },
        "last_updated": "2024-01-01T12:00:00Z"
    }
}
```

#### GET /dashboard
Get dashboard data (similar to financial-summary but optimized for dashboard display).

## Error Responses

All endpoints should return consistent error responses:

```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field_name": ["Validation error message"]
    }
}
```

## HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `422` - Unprocessable Entity (validation errors)
- `500` - Internal Server Error

## Authentication

All endpoints (except login/register) require Bearer token authentication:

```
Authorization: Bearer {jwt_token}
```

## Data Validation Rules

### Account Creation/Update
- `name`: Required, max 255 characters
- `type`: Required, must be one of: cash, bank, upi, credit_card, digital_wallet
- `balance`: Required, decimal with 2 decimal places
- `currency`: Optional, default 'INR', max 3 characters
- `bank_name`: Optional, max 255 characters
- `account_number`: Optional, max 255 characters

### Transaction Creation/Update
- `type`: Required, must be 'income' or 'expense'
- `amount`: Required, positive decimal with 2 decimal places
- `category`: Required, max 255 characters
- `account`: Required, max 255 characters
- `date`: Required, valid ISO 8601 datetime
- `description`: Optional, max 1000 characters
- `notes`: Optional, max 2000 characters

### Category Creation/Update
- `name`: Required, max 255 characters
- `type`: Required, must be 'income' or 'expense'
- `description`: Optional, max 1000 characters
- `icon`: Optional, max 100 characters
- `color`: Optional, valid hex color code

## Implementation Notes

1. **Database Transactions**: Use database transactions for operations that affect multiple tables (e.g., creating a transaction should update account balance).

2. **Balance Calculation**: Account balances should be calculated from transactions, not stored separately (except for initial balance).

3. **Soft Deletes**: Implement soft deletes for accounts and categories to maintain data integrity.

4. **Pagination**: Implement pagination for transaction lists to handle large datasets.

5. **Caching**: Consider implementing caching for financial summaries and category lists.

6. **Rate Limiting**: Implement rate limiting to prevent abuse.

7. **Input Sanitization**: Sanitize all inputs to prevent SQL injection and XSS attacks.

8. **Logging**: Log all financial operations for audit purposes.

9. **Backup**: Implement regular database backups.

10. **Performance**: Add appropriate database indexes for optimal query performance.
