import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../core/app_export.dart';

/// Theme selection dialog
class ThemeSelectionDialog extends StatelessWidget {
  final AppThemeMode currentTheme;
  final Function(AppThemeMode) onThemeSelected;

  const ThemeSelectionDialog({
    Key? key,
    required this.currentTheme,
    required this.onThemeSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          CustomIconWidget(
            iconName: 'palette',
            color: AppTheme.primaryTeal,
            size: 6.w,
          ),
          SizedBox(width: 2.w),
          Text('Select Theme'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: AppThemeMode.values.map((theme) {
          return RadioListTile<AppThemeMode>(
            title: Text(_getThemeTitle(theme)),
            subtitle: Text(_getThemeSubtitle(theme)),
            value: theme,
            groupValue: currentTheme,
            onChanged: (value) {
              if (value != null) {
                onThemeSelected(value);
                Navigator.of(context).pop();
              }
            },
            activeColor: AppTheme.primaryTeal,
          );
        }).toList(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Cancel'),
        ),
      ],
    );
  }

  String _getThemeTitle(AppThemeMode theme) {
    switch (theme) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }

  String _getThemeSubtitle(AppThemeMode theme) {
    switch (theme) {
      case AppThemeMode.light:
        return 'Always use light theme';
      case AppThemeMode.dark:
        return 'Always use dark theme';
      case AppThemeMode.system:
        return 'Follow system setting';
    }
  }
}

/// Currency selection dialog
class CurrencySelectionDialog extends StatelessWidget {
  final SupportedCurrency currentCurrency;
  final Function(SupportedCurrency) onCurrencySelected;

  const CurrencySelectionDialog({
    Key? key,
    required this.currentCurrency,
    required this.onCurrencySelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          CustomIconWidget(
            iconName: 'currency_exchange',
            color: AppTheme.primaryTeal,
            size: 6.w,
          ),
          SizedBox(width: 2.w),
          Text('Select Currency'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: SupportedCurrency.values.length,
          itemBuilder: (context, index) {
            final currency = SupportedCurrency.values[index];
            return RadioListTile<SupportedCurrency>(
              title: Text('${currency.name} (${currency.code})'),
              subtitle: Text('Symbol: ${currency.symbol}'),
              value: currency,
              groupValue: currentCurrency,
              onChanged: (value) {
                if (value != null) {
                  onCurrencySelected(value);
                  Navigator.of(context).pop();
                }
              },
              activeColor: AppTheme.primaryTeal,
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Cancel'),
        ),
      ],
    );
  }
}

/// Date format selection dialog
class DateFormatSelectionDialog extends StatelessWidget {
  final DateFormatType currentFormat;
  final Function(DateFormatType) onFormatSelected;

  const DateFormatSelectionDialog({
    Key? key,
    required this.currentFormat,
    required this.onFormatSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    
    return AlertDialog(
      title: Row(
        children: [
          CustomIconWidget(
            iconName: 'calendar_today',
            color: AppTheme.primaryPurple,
            size: 6.w,
          ),
          SizedBox(width: 2.w),
          Text('Select Date Format'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: DateFormatType.values.map((format) {
          return RadioListTile<DateFormatType>(
            title: Text(format.format),
            subtitle: Text(_getFormattedExample(format, now)),
            value: format,
            groupValue: currentFormat,
            onChanged: (value) {
              if (value != null) {
                onFormatSelected(value);
                Navigator.of(context).pop();
              }
            },
            activeColor: AppTheme.primaryTeal,
          );
        }).toList(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Cancel'),
        ),
      ],
    );
  }

  String _getFormattedExample(DateFormatType format, DateTime date) {
    final settingsService = SettingsService.instance;
    final originalFormat = settingsService.dateFormat;
    
    // Temporarily set the format to get example
    switch (format) {
      case DateFormatType.ddMmYyyy:
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      case DateFormatType.mmDdYyyy:
        return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
      case DateFormatType.yyyyMmDd:
        return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      case DateFormatType.ddMmmYyyy:
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return '${date.day.toString().padLeft(2, '0')} ${months[date.month - 1]} ${date.year}';
    }
  }
}

/// Number format selection dialog
class NumberFormatSelectionDialog extends StatelessWidget {
  final NumberFormatType currentFormat;
  final Function(NumberFormatType) onFormatSelected;

  const NumberFormatSelectionDialog({
    Key? key,
    required this.currentFormat,
    required this.onFormatSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          CustomIconWidget(
            iconName: 'format_list_numbered',
            color: AppTheme.successGreen,
            size: 6.w,
          ),
          SizedBox(width: 2.w),
          Text('Select Number Format'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: NumberFormatType.values.map((format) {
          return RadioListTile<NumberFormatType>(
            title: Text(format.format),
            subtitle: Text(_getFormattedExample(format)),
            value: format,
            groupValue: currentFormat,
            onChanged: (value) {
              if (value != null) {
                onFormatSelected(value);
                Navigator.of(context).pop();
              }
            },
            activeColor: AppTheme.primaryTeal,
          );
        }).toList(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Cancel'),
        ),
      ],
    );
  }

  String _getFormattedExample(NumberFormatType format) {
    const testNumber = 123456.78;
    switch (format) {
      case NumberFormatType.indian:
        return '1,23,456.78';
      case NumberFormatType.international:
        return '123,456.78';
      case NumberFormatType.european:
        return '123.456,78';
    }
  }
}

/// Change password dialog
class ChangePasswordDialog extends StatefulWidget {
  const ChangePasswordDialog({Key? key}) : super(key: key);

  @override
  State<ChangePasswordDialog> createState() => _ChangePasswordDialogState();
}

class _ChangePasswordDialogState extends State<ChangePasswordDialog> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isCurrentPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          CustomIconWidget(
            iconName: 'lock',
            color: AppTheme.alertRed,
            size: 6.w,
          ),
          SizedBox(width: 2.w),
          Text('Change Password'),
        ],
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Current Password
            TextFormField(
              controller: _currentPasswordController,
              obscureText: !_isCurrentPasswordVisible,
              decoration: InputDecoration(
                labelText: 'Current Password',
                prefixIcon: Icon(Icons.lock_outline),
                suffixIcon: IconButton(
                  icon: Icon(_isCurrentPasswordVisible ? Icons.visibility_off : Icons.visibility),
                  onPressed: () => setState(() => _isCurrentPasswordVisible = !_isCurrentPasswordVisible),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your current password';
                }
                return null;
              },
            ),
            SizedBox(height: 2.h),
            
            // New Password
            TextFormField(
              controller: _newPasswordController,
              obscureText: !_isNewPasswordVisible,
              decoration: InputDecoration(
                labelText: 'New Password',
                prefixIcon: Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(_isNewPasswordVisible ? Icons.visibility_off : Icons.visibility),
                  onPressed: () => setState(() => _isNewPasswordVisible = !_isNewPasswordVisible),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a new password';
                }
                if (value.length < 6) {
                  return 'Password must be at least 6 characters';
                }
                return null;
              },
            ),
            SizedBox(height: 2.h),
            
            // Confirm Password
            TextFormField(
              controller: _confirmPasswordController,
              obscureText: !_isConfirmPasswordVisible,
              decoration: InputDecoration(
                labelText: 'Confirm New Password',
                prefixIcon: Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(_isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility),
                  onPressed: () => setState(() => _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please confirm your new password';
                }
                if (value != _newPasswordController.text) {
                  return 'Passwords do not match';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _changePassword,
          child: _isLoading 
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text('Change Password'),
        ),
      ],
    );
  }

  Future<void> _changePassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final authService = AuthService.instance;
      final response = await authService.changePassword(
        currentPassword: _currentPasswordController.text,
        newPassword: _newPasswordController.text,
      );

      if (mounted) {
        if (response.success) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Password changed successfully! Please log in again.'),
              backgroundColor: AppTheme.successGreen,
              duration: Duration(seconds: 4),
            ),
          );

          // Optional: Force logout to make user login with new password
          // This ensures security by invalidating all sessions
          Future.delayed(Duration(seconds: 2), () {
            if (mounted) {
              Navigator.of(context).pushNamedAndRemoveUntil(
                AppRoutes.authenticationScreen,
                (route) => false,
              );
            }
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message),
              backgroundColor: AppTheme.alertRed,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to change password: $e'),
            backgroundColor: AppTheme.alertRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
