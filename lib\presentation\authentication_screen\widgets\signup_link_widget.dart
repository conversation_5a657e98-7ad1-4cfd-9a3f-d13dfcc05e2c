import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class SignupLinkWidget extends StatelessWidget {
  final bool isLoading;
  final bool isRegisterMode;
  final VoidCallback onToggleMode;

  const SignupLinkWidget({
    Key? key,
    required this.isLoading,
    required this.isRegisterMode,
    required this.onToggleMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            isRegisterMode ? 'Already have an account? ' : 'New user? ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.neutralGray,
                ),
          ),
          TextButton(
            onPressed: isLoading ? null : onToggleMode,
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 1.w),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              isRegisterMode ? 'Sign In' : 'Sign Up',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.primaryTeal,
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.underline,
                    decorationColor: AppTheme.primaryTeal,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
