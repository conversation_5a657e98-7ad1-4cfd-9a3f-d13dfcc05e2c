import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class CategorySelector extends StatelessWidget {
  final String? selectedCategory;
  final bool isIncome;
  final ValueChanged<String> onCategorySelected;

  const CategorySelector({
    Key? key,
    this.selectedCategory,
    required this.isIncome,
    required this.onCategorySelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categories = isIncome ? _incomeCategories : _expenseCategories;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Category',
            style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.h),
          SizedBox(
            height: 12.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = selectedCategory == category['name'];

                return GestureDetector(
                  onTap: () => onCategorySelected(category['name'] as String),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    margin: EdgeInsets.only(right: 3.w),
                    padding:
                        EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: isSelected
                          ? (isIncome
                              ? AppTheme.successGreen
                              : AppTheme.alertRed)
                          : AppTheme.lightTheme.colorScheme.surface,
                      border: Border.all(
                        color: isSelected
                            ? (isIncome
                                ? AppTheme.successGreen
                                : AppTheme.alertRed)
                            : AppTheme.borderSubtle,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomIconWidget(
                          iconName: category['icon'] as String,
                          color: isSelected
                              ? Colors.white
                              : (isIncome
                                  ? AppTheme.successGreen
                                  : AppTheme.alertRed),
                          size: 24,
                        ),
                        SizedBox(height: 1.h),
                        Text(
                          category['name'] as String,
                          style:
                              AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                            color: isSelected
                                ? Colors.white
                                : AppTheme.textHighEmphasisLight,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(height: 1.h),
          GestureDetector(
            onTap: () => _showAllCategories(context),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.primaryTeal),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomIconWidget(
                    iconName: 'apps',
                    color: AppTheme.primaryTeal,
                    size: 20,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    'View All Categories',
                    style: AppTheme.lightTheme.textTheme.titleSmall?.copyWith(
                      color: AppTheme.primaryTeal,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAllCategories(BuildContext context) {
    final allCategories = isIncome ? _incomeCategories : _expenseCategories;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 70.h,
        decoration: BoxDecoration(
          color: AppTheme.lightTheme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 1.h),
              width: 12.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: AppTheme.borderSubtle,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Text(
                'Select Category',
                style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.symmetric(horizontal: 4.w),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 3.w,
                  mainAxisSpacing: 2.h,
                  childAspectRatio: 1,
                ),
                itemCount: allCategories.length,
                itemBuilder: (context, index) {
                  final category = allCategories[index];
                  final isSelected = selectedCategory == category['name'];

                  return GestureDetector(
                    onTap: () {
                      onCategorySelected(category['name'] as String);
                      Navigator.pop(context);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: isSelected
                            ? (isIncome
                                ? AppTheme.successGreen
                                : AppTheme.alertRed)
                            : AppTheme.lightTheme.colorScheme.surface,
                        border: Border.all(
                          color: isSelected
                              ? (isIncome
                                  ? AppTheme.successGreen
                                  : AppTheme.alertRed)
                              : AppTheme.borderSubtle,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomIconWidget(
                            iconName: category['icon'] as String,
                            color: isSelected
                                ? Colors.white
                                : (isIncome
                                    ? AppTheme.successGreen
                                    : AppTheme.alertRed),
                            size: 28,
                          ),
                          SizedBox(height: 1.h),
                          Text(
                            category['name'] as String,
                            style: AppTheme.lightTheme.textTheme.bodySmall
                                ?.copyWith(
                              color: isSelected
                                  ? Colors.white
                                  : AppTheme.textHighEmphasisLight,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  static final List<Map<String, dynamic>> _incomeCategories = [
    {"name": "Salary", "icon": "work"},
    {"name": "Freelance", "icon": "laptop_mac"},
    {"name": "Investment", "icon": "trending_up"},
    {"name": "Business", "icon": "business"},
    {"name": "Rental", "icon": "home"},
    {"name": "Gift", "icon": "card_giftcard"},
    {"name": "Bonus", "icon": "star"},
    {"name": "Interest", "icon": "account_balance"},
    {"name": "Dividend", "icon": "pie_chart"},
    {"name": "Other", "icon": "more_horiz"},
  ];

  static final List<Map<String, dynamic>> _expenseCategories = [
    {"name": "Food", "icon": "restaurant"},
    {"name": "Transport", "icon": "directions_car"},
    {"name": "Shopping", "icon": "shopping_bag"},
    {"name": "Bills", "icon": "receipt"},
    {"name": "Entertainment", "icon": "movie"},
    {"name": "Health", "icon": "local_hospital"},
    {"name": "Education", "icon": "school"},
    {"name": "Travel", "icon": "flight"},
    {"name": "Groceries", "icon": "local_grocery_store"},
    {"name": "Fuel", "icon": "local_gas_station"},
    {"name": "Insurance", "icon": "security"},
    {"name": "Other", "icon": "more_horiz"},
  ];
}
