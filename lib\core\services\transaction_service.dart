import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/transaction_model.dart';
import 'http_client_service.dart';

/// Service for managing transactions
class TransactionService {
  static TransactionService? _instance;
  final HttpClientService _httpClient = HttpClientService.instance;

  /// Singleton instance
  static TransactionService get instance {
    _instance ??= TransactionService._internal();
    return _instance!;
  }

  TransactionService._internal();

  /// Get all transactions for the authenticated user
  Future<ApiResponse<List<Transaction>>> getTransactions({
    int? limit,
    int? offset,
    String? type, // 'income' or 'expense'
    String? category,
    String? account,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();
      if (type != null) queryParams['type'] = type;
      if (category != null) queryParams['category'] = category;
      if (account != null) queryParams['account'] = account;
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _httpClient.get<List<Transaction>>(
        ApiConfig.transactionsEndpoint,
        queryParameters: queryParams,
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => Transaction.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Get transactions error: $e');
      return ApiResponse.error(
        message: 'Failed to get transactions',
        error: e.toString(),
      );
    }
  }

  /// Get a specific transaction by ID
  Future<ApiResponse<Transaction>> getTransaction(int transactionId) async {
    try {
      final response = await _httpClient.get<Transaction>(
        '${ApiConfig.transactionsEndpoint}/$transactionId',
        fromJson: (json) => Transaction.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Get transaction error: $e');
      return ApiResponse.error(
        message: 'Failed to get transaction',
        error: e.toString(),
      );
    }
  }

  /// Create a new transaction
  Future<ApiResponse<Transaction>> createTransaction({
    required String type,
    required double amount,
    required String category,
    String? subcategory,
    required String account,
    String? description,
    String? notes,
    required DateTime date,
    bool isRecurring = false,
    String? recurringFrequency,
    DateTime? recurringEndDate,
    String? tags,
    String? location,
  }) async {
    try {
      final data = {
        'type': type,
        'amount': amount,
        'category': category,
        'account': account,
        'date': date.toIso8601String(),
        'is_recurring': isRecurring ? 1 : 0,
      };

      if (subcategory != null) data['subcategory'] = subcategory;
      if (description != null) data['description'] = description;
      if (notes != null) data['notes'] = notes;
      if (recurringFrequency != null) data['recurring_frequency'] = recurringFrequency;
      if (recurringEndDate != null) data['recurring_end_date'] = recurringEndDate.toIso8601String();
      if (tags != null) data['tags'] = tags;
      if (location != null) data['location'] = location;

      final response = await _httpClient.post<Transaction>(
        ApiConfig.transactionsEndpoint,
        data: data,
        fromJson: (json) => Transaction.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Create transaction error: $e');
      return ApiResponse.error(
        message: 'Failed to create transaction',
        error: e.toString(),
      );
    }
  }

  /// Update an existing transaction
  Future<ApiResponse<Transaction>> updateTransaction({
    required int transactionId,
    String? type,
    double? amount,
    String? category,
    String? subcategory,
    String? account,
    String? description,
    String? notes,
    DateTime? date,
    bool? isRecurring,
    String? recurringFrequency,
    DateTime? recurringEndDate,
    String? tags,
    String? location,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (type != null) data['type'] = type;
      if (amount != null) data['amount'] = amount;
      if (category != null) data['category'] = category;
      if (subcategory != null) data['subcategory'] = subcategory;
      if (account != null) data['account'] = account;
      if (description != null) data['description'] = description;
      if (notes != null) data['notes'] = notes;
      if (date != null) data['date'] = date.toIso8601String();
      if (isRecurring != null) data['is_recurring'] = isRecurring ? 1 : 0;
      if (recurringFrequency != null) data['recurring_frequency'] = recurringFrequency;
      if (recurringEndDate != null) data['recurring_end_date'] = recurringEndDate.toIso8601String();
      if (tags != null) data['tags'] = tags;
      if (location != null) data['location'] = location;

      final response = await _httpClient.put<Transaction>(
        '${ApiConfig.transactionsEndpoint}/$transactionId',
        data: data,
        fromJson: (json) => Transaction.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Update transaction error: $e');
      return ApiResponse.error(
        message: 'Failed to update transaction',
        error: e.toString(),
      );
    }
  }

  /// Delete a transaction
  Future<ApiResponse<void>> deleteTransaction(int transactionId) async {
    try {
      final response = await _httpClient.delete<void>(
        '${ApiConfig.transactionsEndpoint}/$transactionId',
      );

      return response;
    } catch (e) {
      debugPrint('Delete transaction error: $e');
      return ApiResponse.error(
        message: 'Failed to delete transaction',
        error: e.toString(),
      );
    }
  }

  /// Get recent transactions (last 10)
  Future<ApiResponse<List<Transaction>>> getRecentTransactions() async {
    return getTransactions(limit: 10, offset: 0);
  }

  /// Get transactions for current month
  Future<ApiResponse<List<Transaction>>> getMonthlyTransactions([DateTime? month]) async {
    final targetMonth = month ?? DateTime.now();
    final startDate = DateTime(targetMonth.year, targetMonth.month, 1);
    final endDate = DateTime(targetMonth.year, targetMonth.month + 1, 0, 23, 59, 59);

    return getTransactions(
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Get transactions for current week
  Future<ApiResponse<List<Transaction>>> getWeeklyTransactions([DateTime? week]) async {
    final targetDate = week ?? DateTime.now();
    final startDate = targetDate.subtract(Duration(days: targetDate.weekday - 1));
    final endDate = startDate.add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));

    return getTransactions(
      startDate: DateTime(startDate.year, startDate.month, startDate.day),
      endDate: endDate,
    );
  }

  /// Get transactions by category
  Future<ApiResponse<List<Transaction>>> getTransactionsByCategory(String category) async {
    return getTransactions(category: category);
  }

  /// Get transactions by account
  Future<ApiResponse<List<Transaction>>> getTransactionsByAccount(String account) async {
    return getTransactions(account: account);
  }

  /// Search transactions
  Future<ApiResponse<List<Transaction>>> searchTransactions({
    String? query,
    String? type,
    double? minAmount,
    double? maxAmount,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (query != null) queryParams['search'] = query;
      if (type != null) queryParams['type'] = type;
      if (minAmount != null) queryParams['min_amount'] = minAmount.toString();
      if (maxAmount != null) queryParams['max_amount'] = maxAmount.toString();
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _httpClient.get<List<Transaction>>(
        '${ApiConfig.transactionsEndpoint}/search',
        queryParameters: queryParams,
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => Transaction.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Search transactions error: $e');
      return ApiResponse.error(
        message: 'Failed to search transactions',
        error: e.toString(),
      );
    }
  }
}
