import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../app_export.dart';
import 'settings_service.dart';

/// Theme service for managing app themes
class ThemeService extends ChangeNotifier {
  static ThemeService? _instance;
  final SettingsService _settingsService = SettingsService.instance;

  /// Singleton instance
  static ThemeService get instance {
    _instance ??= ThemeService._internal();
    return _instance!;
  }

  ThemeService._internal() {
    _settingsService.addListener(_onSettingsChanged);
  }

  /// Handle settings changes
  void _onSettingsChanged() {
    notifyListeners();
  }

  /// Get current theme mode
  ThemeMode get themeMode => _settingsService.flutterThemeMode;

  /// Get light theme
  ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppTheme.primaryTeal,
        brightness: Brightness.light,
      ),
      scaffoldBackgroundColor: AppTheme.backgroundLight,
      appBarTheme: AppBarTheme(
        backgroundColor: AppTheme.backgroundLight,
        foregroundColor: AppTheme.textHighEmphasisLight,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: TextStyle(
          color: AppTheme.textHighEmphasisLight,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      cardTheme: CardThemeData(
        color: AppTheme.cardSurface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryTeal,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppTheme.primaryTeal,
          side: BorderSide(color: AppTheme.primaryTeal),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppTheme.primaryTeal,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppTheme.cardSurface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppTheme.borderSubtle),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppTheme.borderSubtle),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppTheme.primaryTeal, width: 2),
        ),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppTheme.primaryTeal;
          }
          return AppTheme.neutralGray;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppTheme.primaryTeal.withOpacity(0.3);
          }
          return AppTheme.neutralGray.withOpacity(0.3);
        }),
      ),
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          color: AppTheme.textHighEmphasisLight,
          fontSize: 32,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: AppTheme.textHighEmphasisLight,
          fontSize: 28,
          fontWeight: FontWeight.w600,
        ),
        headlineSmall: TextStyle(
          color: AppTheme.textHighEmphasisLight,
          fontSize: 24,
          fontWeight: FontWeight.w600,
        ),
        titleLarge: TextStyle(
          color: AppTheme.textHighEmphasisLight,
          fontSize: 22,
          fontWeight: FontWeight.w500,
        ),
        titleMedium: TextStyle(
          color: AppTheme.textHighEmphasisLight,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        titleSmall: TextStyle(
          color: AppTheme.textMediumEmphasisLight,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        bodyLarge: TextStyle(
          color: AppTheme.textHighEmphasisLight,
          fontSize: 16,
        ),
        bodyMedium: TextStyle(
          color: AppTheme.textMediumEmphasisLight,
          fontSize: 14,
        ),
        bodySmall: TextStyle(
          color: AppTheme.textLowEmphasisLight,
          fontSize: 12,
        ),
        labelLarge: TextStyle(
          color: AppTheme.textHighEmphasisLight,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        labelMedium: TextStyle(
          color: AppTheme.textMediumEmphasisLight,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        labelSmall: TextStyle(
          color: AppTheme.textLowEmphasisLight,
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Get dark theme
  ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppTheme.primaryTeal,
        brightness: Brightness.dark,
      ),
      scaffoldBackgroundColor: AppTheme.backgroundDark,
      appBarTheme: AppBarTheme(
        backgroundColor: AppTheme.backgroundDark,
        foregroundColor: AppTheme.textHighEmphasisDark,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          color: AppTheme.textHighEmphasisDark,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      cardTheme: CardThemeData(
        color: AppTheme.cardSurfaceDark,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryTeal,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppTheme.primaryTeal,
          side: BorderSide(color: AppTheme.primaryTeal),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppTheme.primaryTeal,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppTheme.cardSurfaceDark,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppTheme.dividerDark),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppTheme.dividerDark),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppTheme.primaryTeal, width: 2),
        ),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppTheme.primaryTeal;
          }
          return AppTheme.neutralGray;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppTheme.primaryTeal.withOpacity(0.3);
          }
          return AppTheme.neutralGray.withOpacity(0.3);
        }),
      ),
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          color: AppTheme.textHighEmphasisDark,
          fontSize: 32,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: AppTheme.textHighEmphasisDark,
          fontSize: 28,
          fontWeight: FontWeight.w600,
        ),
        headlineSmall: TextStyle(
          color: AppTheme.textHighEmphasisDark,
          fontSize: 24,
          fontWeight: FontWeight.w600,
        ),
        titleLarge: TextStyle(
          color: AppTheme.textHighEmphasisDark,
          fontSize: 22,
          fontWeight: FontWeight.w500,
        ),
        titleMedium: TextStyle(
          color: AppTheme.textHighEmphasisDark,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        titleSmall: TextStyle(
          color: AppTheme.textMediumEmphasisDark,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        bodyLarge: TextStyle(
          color: AppTheme.textHighEmphasisDark,
          fontSize: 16,
        ),
        bodyMedium: TextStyle(
          color: AppTheme.textMediumEmphasisDark,
          fontSize: 14,
        ),
        bodySmall: TextStyle(
          color: AppTheme.textLowEmphasisDark,
          fontSize: 12,
        ),
        labelLarge: TextStyle(
          color: AppTheme.textHighEmphasisDark,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        labelMedium: TextStyle(
          color: AppTheme.textMediumEmphasisDark,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        labelSmall: TextStyle(
          color: AppTheme.textLowEmphasisDark,
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _settingsService.removeListener(_onSettingsChanged);
    super.dispose();
  }
}
