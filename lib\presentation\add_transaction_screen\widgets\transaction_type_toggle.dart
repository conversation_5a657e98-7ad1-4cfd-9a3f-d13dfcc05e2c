import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../../core/app_export.dart';

class TransactionTypeToggle extends StatelessWidget {
  final bool isIncome;
  final ValueChanged<bool> onToggle;

  const TransactionTypeToggle({
    Key? key,
    required this.isIncome,
    required this.onToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppTheme.lightTheme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: AppTheme.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => onToggle(true),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: EdgeInsets.symmetric(vertical: 2.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: isIncome ? AppTheme.successGreen : Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomIconWidget(
                      iconName: 'trending_up',
                      color: isIncome ? Colors.white : AppTheme.successGreen,
                      size: 20,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      'Income',
                      style:
                          AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        color: isIncome ? Colors.white : AppTheme.successGreen,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => onToggle(false),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: EdgeInsets.symmetric(vertical: 2.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: !isIncome ? AppTheme.alertRed : Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomIconWidget(
                      iconName: 'trending_down',
                      color: !isIncome ? Colors.white : AppTheme.alertRed,
                      size: 20,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      'Expense',
                      style:
                          AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        color: !isIncome ? Colors.white : AppTheme.alertRed,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
