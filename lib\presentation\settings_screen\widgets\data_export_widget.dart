import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class DataExportWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onExportData;

  const DataExportWidget({
    super.key,
    required this.onExportData,
  });

  @override
  State<DataExportWidget> createState() => _DataExportWidgetState();
}

class _DataExportWidgetState extends State<DataExportWidget> {
  String selectedFormat = 'JSON';
  String selectedDateRange = 'All Time';
  String selectedAccount = 'All Accounts';
  String selectedCategory = 'All Categories';

  final List<String> formats = ['JSON', 'CSV', 'PDF'];
  final List<String> dateRanges = [
    'All Time',
    'Last 30 Days',
    'Last 90 Days',
    'This Year',
    'Custom Range'
  ];
  final List<String> accounts = [
    'All Accounts',
    'Cash',
    'Bank Account',
    'UPI',
    'Credit Card'
  ];
  final List<String> categories = [
    'All Categories',
    'Food',
    'Transport',
    'Shopping',
    'Bills',
    'Entertainment'
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
          child: Text(
            "Export Data",
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppTheme.primaryPurple,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
          ),
        ),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          padding: EdgeInsets.all(4.w),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Export your financial data with customizable filters",
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
              SizedBox(height: 3.h),

              // Format Selection
              _buildFilterSection(
                context,
                "Format",
                selectedFormat,
                formats,
                (value) => setState(() => selectedFormat = value),
                'description',
              ),

              SizedBox(height: 2.h),

              // Date Range Selection
              _buildFilterSection(
                context,
                "Date Range",
                selectedDateRange,
                dateRanges,
                (value) => setState(() => selectedDateRange = value),
                'date_range',
              ),

              SizedBox(height: 2.h),

              // Account Selection
              _buildFilterSection(
                context,
                "Account",
                selectedAccount,
                accounts,
                (value) => setState(() => selectedAccount = value),
                'account_balance_wallet',
              ),

              SizedBox(height: 2.h),

              // Category Selection
              _buildFilterSection(
                context,
                "Category",
                selectedCategory,
                categories,
                (value) => setState(() => selectedCategory = value),
                'category',
              ),

              SizedBox(height: 3.h),

              // Export Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    widget.onExportData({
                      'format': selectedFormat,
                      'dateRange': selectedDateRange,
                      'account': selectedAccount,
                      'category': selectedCategory,
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryPurple,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  icon: CustomIconWidget(
                    iconName: 'download',
                    color: Colors.white,
                    size: 5.w,
                  ),
                  label: Text(
                    "Export Data",
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 2.h),
      ],
    );
  }

  Widget _buildFilterSection(
    BuildContext context,
    String title,
    String selectedValue,
    List<String> options,
    ValueChanged<String> onChanged,
    String iconName,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomIconWidget(
              iconName: iconName,
              color: AppTheme.primaryTeal,
              size: 4.w,
            ),
            SizedBox(width: 2.w),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
        SizedBox(height: 1.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedValue,
              isExpanded: true,
              icon: CustomIconWidget(
                iconName: 'keyboard_arrow_down',
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 5.w,
              ),
              items: options.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  onChanged(newValue);
                }
              },
            ),
          ),
        ),
      ],
    );
  }
}
