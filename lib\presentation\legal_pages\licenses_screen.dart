import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../core/app_export.dart';

class LicensesScreen extends StatelessWidget {
  const LicensesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppTheme.primaryTeal,
            size: 6.w,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Open Source Licenses',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppTheme.primaryTeal,
                fontWeight: FontWeight.w600,
              ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: AppTheme.primaryTeal.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.primaryTeal.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.code,
                    color: AppTheme.primaryTeal,
                    size: 8.w,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'Third-Party Licenses',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppTheme.primaryTeal,
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'FinTrack uses the following open source libraries',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.neutralGray,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            SizedBox(height: 4.h),

            // Flutter Framework
            _buildLicenseCard(
              context,
              'Flutter',
              'Google Inc.',
              'BSD 3-Clause License',
              'Flutter is Google\'s UI toolkit for building beautiful, natively compiled applications for mobile, web, and desktop from a single codebase.',
            ),

            // Sizer
            _buildLicenseCard(
              context,
              'Sizer',
              'TechnoVerse',
              'MIT License',
              'A flutter plugin for Easily make Flutter apps responsive. Automatically adapt UI to different screen sizes.',
            ),

            // HTTP
            _buildLicenseCard(
              context,
              'HTTP',
              'Dart Team',
              'BSD 3-Clause License',
              'A composable, multi-platform, Future-based API for HTTP requests.',
            ),

            // Shared Preferences
            _buildLicenseCard(
              context,
              'Shared Preferences',
              'Flutter Team',
              'BSD 3-Clause License',
              'Flutter plugin for reading and writing simple key-value pairs.',
            ),

            // URL Launcher
            _buildLicenseCard(
              context,
              'URL Launcher',
              'Flutter Team',
              'BSD 3-Clause License',
              'Flutter plugin for launching a URL in the mobile platform.',
            ),

            // Cupertino Icons
            _buildLicenseCard(
              context,
              'Cupertino Icons',
              'Flutter Team',
              'MIT License',
              'Default icons asset for Cupertino widgets based on Apple styled icons.',
            ),

            SizedBox(height: 4.h),

            // View All Licenses Button
            Container(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  showLicensePage(
                    context: context,
                    applicationName: 'FinTrack',
                    applicationVersion: '1.0.0',
                    applicationIcon: Container(
                      width: 15.w,
                      height: 15.w,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryTeal,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.account_balance_wallet,
                        color: Colors.white,
                        size: 8.w,
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryTeal,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 2.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                icon: Icon(Icons.list_alt, size: 5.w),
                label: Text(
                  'View All Licenses',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ),

            SizedBox(height: 4.h),

            // Footer
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: AppTheme.neutralGray.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'We are grateful to the open source community for their contributions that make FinTrack possible.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.neutralGray,
                      fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  Widget _buildLicenseCard(
    BuildContext context,
    String name,
    String author,
    String license,
    String description,
  ) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: 3.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.neutralGray.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.neutralGray.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.primaryTeal.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.library_books,
                  color: AppTheme.primaryTeal,
                  size: 5.w,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppTheme.primaryTeal,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    Text(
                      'by $author',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.neutralGray,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: AppTheme.successGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              license,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.successGreen,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.neutralGray,
                  height: 1.4,
                ),
          ),
        ],
      ),
    );
  }
}
