import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../core/app_export.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppTheme.primaryTeal,
            size: 6.w,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Privacy Policy',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppTheme.primaryTeal,
                fontWeight: FontWeight.w600,
              ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: AppTheme.primaryTeal.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.primaryTeal.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.privacy_tip,
                    color: AppTheme.primaryTeal,
                    size: 8.w,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'FinTrack Privacy Policy',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppTheme.primaryTeal,
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Last updated: ${DateTime.now().year}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.neutralGray,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            SizedBox(height: 4.h),

            // Privacy Policy Content
            _buildSection(
              context,
              '1. Information We Collect',
              'We collect information you provide directly to us, such as:\n• Account information (email, name)\n• Financial data you input (transactions, budgets)\n• App usage data and preferences\n• Device information for app functionality',
            ),

            _buildSection(
              context,
              '2. How We Use Your Information',
              'We use your information to:\n• Provide and maintain the FinTrack service\n• Process your financial data and generate reports\n• Improve our app and user experience\n• Send important updates and notifications\n• Provide customer support',
            ),

            _buildSection(
              context,
              '3. Data Security',
              'We implement appropriate security measures to protect your personal information:\n• Encryption of sensitive data\n• Secure data transmission\n• Regular security updates\n• Limited access to personal data\n• Secure cloud storage practices',
            ),

            _buildSection(
              context,
              '4. Data Sharing',
              'We do not sell, trade, or rent your personal information to third parties. We may share your information only in these limited circumstances:\n• With your explicit consent\n• To comply with legal obligations\n• To protect our rights and safety\n• With service providers under strict confidentiality agreements',
            ),

            _buildSection(
              context,
              '5. Data Retention',
              'We retain your personal information for as long as your account is active or as needed to provide services. You can request deletion of your account and associated data at any time through the app settings.',
            ),

            _buildSection(
              context,
              '6. Your Rights',
              'You have the right to:\n• Access your personal data\n• Correct inaccurate information\n• Delete your account and data\n• Export your data\n• Opt-out of non-essential communications\n• Request information about data processing',
            ),

            _buildSection(
              context,
              '7. Cookies and Analytics',
              'We may use analytics tools to understand how users interact with our app. This helps us improve functionality and user experience. No personally identifiable information is shared with analytics providers.',
            ),

            _buildSection(
              context,
              '8. Children\'s Privacy',
              'FinTrack is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If we become aware of such collection, we will delete the information immediately.',
            ),

            _buildSection(
              context,
              '9. International Data Transfers',
              'Your information may be transferred to and processed in countries other than your own. We ensure appropriate safeguards are in place to protect your data during such transfers.',
            ),

            _buildSection(
              context,
              '10. Changes to Privacy Policy',
              'We may update this Privacy Policy from time to time. We will notify you of any material changes through the app or via email. Your continued use of FinTrack after changes constitutes acceptance of the updated policy.',
            ),

            _buildSection(
              context,
              '11. Contact Us',
              'If you have any questions about this Privacy Policy or our data practices, please contact us through the app\'s support feature or at our official support channels.',
            ),

            SizedBox(height: 4.h),

            // Footer
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: AppTheme.neutralGray.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Your privacy is important to us. We are committed to protecting your personal information and being transparent about our data practices.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.neutralGray,
                      fontStyle: FontStyle.italic,
                    ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: EdgeInsets.only(bottom: 3.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppTheme.primaryTeal,
                  fontWeight: FontWeight.w600,
                ),
          ),
          SizedBox(height: 1.h),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.neutralGray,
                  height: 1.5,
                ),
          ),
        ],
      ),
    );
  }
}
