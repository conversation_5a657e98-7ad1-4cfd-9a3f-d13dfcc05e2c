import 'dart:convert';

/// Category model representing transaction categories
class Category {
  final int? id;
  final int? userId; // null for system categories, user_id for custom categories
  final String name;
  final String type; // 'income' or 'expense'
  final String? parentCategory;
  final String? description;
  final String? icon;
  final String? color;
  final bool isActive;
  final bool isSystem; // true for default categories, false for user-created
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Category({
    this.id,
    this.userId,
    required this.name,
    required this.type,
    this.parentCategory,
    this.description,
    this.icon,
    this.color,
    this.isActive = true,
    this.isSystem = false,
    this.sortOrder = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create Category from JSON
  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] is String ? int.parse(json['id']) : json['id'],
      userId: json['user_id'] is String ? int.parse(json['user_id']) : json['user_id'],
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      parentCategory: json['parent_category'],
      description: json['description'],
      icon: json['icon'],
      color: json['color'],
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      isSystem: json['is_system'] == 1 || json['is_system'] == true,
      sortOrder: json['sort_order'] is String ? int.parse(json['sort_order']) : (json['sort_order'] ?? 0),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  /// Convert Category to JSON
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (userId != null) 'user_id': userId,
      'name': name,
      'type': type,
      if (parentCategory != null) 'parent_category': parentCategory,
      if (description != null) 'description': description,
      if (icon != null) 'icon': icon,
      if (color != null) 'color': color,
      'is_active': isActive ? 1 : 0,
      'is_system': isSystem ? 1 : 0,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Convert Category to JSON string
  String toJsonString() {
    return json.encode(toJson());
  }

  /// Create Category from JSON string
  factory Category.fromJsonString(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return Category.fromJson(json);
  }

  /// Create a copy of Category with updated fields
  Category copyWith({
    int? id,
    int? userId,
    String? name,
    String? type,
    String? parentCategory,
    String? description,
    String? icon,
    String? color,
    bool? isActive,
    bool? isSystem,
    int? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      type: type ?? this.type,
      parentCategory: parentCategory ?? this.parentCategory,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      isSystem: isSystem ?? this.isSystem,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if category is for income
  bool get isIncome => type.toLowerCase() == 'income';

  /// Check if category is for expense
  bool get isExpense => type.toLowerCase() == 'expense';

  /// Get category icon name
  String get iconName {
    if (icon != null) return icon!;
    
    // Default icons based on common category names
    final categoryName = name.toLowerCase();
    
    if (isIncome) {
      if (categoryName.contains('salary')) return 'work';
      if (categoryName.contains('business')) return 'business';
      if (categoryName.contains('investment')) return 'trending_up';
      if (categoryName.contains('freelance')) return 'laptop';
      return 'attach_money';
    } else {
      if (categoryName.contains('food')) return 'restaurant';
      if (categoryName.contains('transport')) return 'directions_car';
      if (categoryName.contains('shopping')) return 'shopping_cart';
      if (categoryName.contains('entertainment')) return 'movie';
      if (categoryName.contains('health')) return 'local_hospital';
      if (categoryName.contains('education')) return 'school';
      if (categoryName.contains('utility') || categoryName.contains('bill')) return 'receipt';
      return 'category';
    }
  }

  /// Get category color
  String get categoryColor {
    if (color != null) return color!;
    
    // Default colors based on type and common categories
    if (isIncome) {
      return '#4CAF50'; // Green for income
    } else {
      final categoryName = name.toLowerCase();
      if (categoryName.contains('food')) return '#FF9800'; // Orange
      if (categoryName.contains('transport')) return '#2196F3'; // Blue
      if (categoryName.contains('shopping')) return '#E91E63'; // Pink
      if (categoryName.contains('entertainment')) return '#9C27B0'; // Purple
      if (categoryName.contains('health')) return '#F44336'; // Red
      if (categoryName.contains('education')) return '#00BCD4'; // Teal
      return '#757575'; // Gray for others
    }
  }

  /// Get display name with parent category if available
  String get displayName {
    if (parentCategory != null && parentCategory!.isNotEmpty) {
      return '$parentCategory > $name';
    }
    return name;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category &&
        other.id == id &&
        other.name == name &&
        other.type == type &&
        other.userId == userId;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, type, userId);
  }

  @override
  String toString() {
    return 'Category(id: $id, name: $name, type: $type, userId: $userId)';
  }
}

/// Default system categories
class DefaultCategories {
  static List<Category> get expenseCategories => [
    Category(
      name: 'Food & Dining',
      type: 'expense',
      icon: 'restaurant',
      color: '#FF9800',
      isSystem: true,
      sortOrder: 1,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Transportation',
      type: 'expense',
      icon: 'directions_car',
      color: '#2196F3',
      isSystem: true,
      sortOrder: 2,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Shopping',
      type: 'expense',
      icon: 'shopping_cart',
      color: '#E91E63',
      isSystem: true,
      sortOrder: 3,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Entertainment',
      type: 'expense',
      icon: 'movie',
      color: '#9C27B0',
      isSystem: true,
      sortOrder: 4,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Bills & Utilities',
      type: 'expense',
      icon: 'receipt',
      color: '#607D8B',
      isSystem: true,
      sortOrder: 5,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Healthcare',
      type: 'expense',
      icon: 'local_hospital',
      color: '#F44336',
      isSystem: true,
      sortOrder: 6,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Education',
      type: 'expense',
      icon: 'school',
      color: '#00BCD4',
      isSystem: true,
      sortOrder: 7,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Other',
      type: 'expense',
      icon: 'category',
      color: '#757575',
      isSystem: true,
      sortOrder: 99,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  static List<Category> get incomeCategories => [
    Category(
      name: 'Salary',
      type: 'income',
      icon: 'work',
      color: '#4CAF50',
      isSystem: true,
      sortOrder: 1,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Business',
      type: 'income',
      icon: 'business',
      color: '#4CAF50',
      isSystem: true,
      sortOrder: 2,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Freelance',
      type: 'income',
      icon: 'laptop',
      color: '#4CAF50',
      isSystem: true,
      sortOrder: 3,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Investment',
      type: 'income',
      icon: 'trending_up',
      color: '#4CAF50',
      isSystem: true,
      sortOrder: 4,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      name: 'Other',
      type: 'income',
      icon: 'attach_money',
      color: '#4CAF50',
      isSystem: true,
      sortOrder: 99,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  static List<Category> get allCategories => [
    ...expenseCategories,
    ...incomeCategories,
  ];
}
