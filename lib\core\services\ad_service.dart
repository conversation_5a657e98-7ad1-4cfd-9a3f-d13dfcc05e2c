import 'dart:io';

import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdService {
  // PRODUCTION AD UNITS - FinTrack App
  static String? get bannerAdUnitId {
    if (Platform.isAndroid) {
      return 'ca-app-pub-7010023287510094/6836990569'; // Fintrack-banner-ad
    } else if (Platform.isIOS) {
      return 'ca-app-pub-7010023287510094/6836990569'; // Same for iOS (update if different iOS ID provided)
    }
    return null;
  }

  static String? get interstitialAdUnitId {
    if (Platform.isAndroid) {
      return 'ca-app-pub-7010023287510094/6717571069'; // FinTrack-interstitial
    } else if (Platform.isIOS) {
      return 'ca-app-pub-7010023287510094/6717571069'; // Same for iOS (update if different iOS ID provided)
    }
    return null;
  }

  static String? get rewardedAdUnitId {
    if (Platform.isAndroid) {
      return 'ca-app-pub-7010023287510094/1576925023'; // FinTrack-rewarded
    } else if (Platform.isIOS) {
      return 'ca-app-pub-7010023287510094/1576925023'; // Same for iOS (update if different iOS ID provided)
    }
    return null;
  }

  static String? get nativeAdUnitId {
    if (Platform.isAndroid) {
      return 'ca-app-pub-7010023287510094/6200187005'; // Fintrack-Native
    } else if (Platform.isIOS) {
      return 'ca-app-pub-7010023287510094/6200187005'; // Same for iOS (update if different iOS ID provided)
    }
    return null;
  }

  static final AdService _instance = AdService._internal();
  factory AdService() => _instance;
  AdService._internal();

  InterstitialAd? _interstitialAd;
  bool _isInterstitialAdReady = false;
  RewardedAd? _rewardedAd;
  bool _isRewardedAdReady = false;

  Future<void> initialize() async {
    print('🎯 AdService: Initializing with production ad units...');
    await MobileAds.instance.initialize();
    print('🎯 AdService: MobileAds initialized successfully');
    _preloadInterstitialAd();
    _preloadRewardedAd();
    print('🎯 AdService: Ad preloading started');
  }

  void _preloadInterstitialAd() {
    final adUnitId = interstitialAdUnitId;
    if (adUnitId == null) {
      print('❌ AdService: Interstitial ad unit ID is null');
      return;
    }

    print('🎯 AdService: Loading interstitial ad with ID: $adUnitId');
    InterstitialAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialAdReady = true;
          print('✅ AdService: Interstitial ad loaded successfully');
        },
        onAdFailedToLoad: (err) {
          _isInterstitialAdReady = false;
          print('❌ AdService: Interstitial ad failed to load: ${err.message}');
        },
      ),
    );
  }

  void showInterstitialAd() {
    if (_isInterstitialAdReady && _interstitialAd != null) {
      print('🎯 AdService: Showing interstitial ad');
      _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdDismissedFullScreenContent: (ad) {
          print('✅ AdService: Interstitial ad dismissed');
          ad.dispose();
          _preloadInterstitialAd();
        },
        onAdFailedToShowFullScreenContent: (ad, err) {
          print('❌ AdService: Interstitial ad failed to show: ${err.message}');
          ad.dispose();
          _preloadInterstitialAd();
        },
      );
      _interstitialAd!.show();
      _isInterstitialAdReady = false;
    } else {
      print('❌ AdService: Interstitial ad not ready or null');
    }
  }

  void _preloadRewardedAd() {
    final adUnitId = rewardedAdUnitId;
    if (adUnitId == null) {
      print('❌ AdService: Rewarded ad unit ID is null');
      return;
    }

    print('🎯 AdService: Loading rewarded ad with ID: $adUnitId');
    RewardedAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          _isRewardedAdReady = true;
          print('✅ AdService: Rewarded ad loaded successfully');
        },
        onAdFailedToLoad: (err) {
          _isRewardedAdReady = false;
          print('❌ AdService: Rewarded ad failed to load: ${err.message}');
        },
      ),
    );
  }

  void showRewardedAd(Function onUserEarnedReward) {
    if (_isRewardedAdReady && _rewardedAd != null) {
      print('🎯 AdService: Showing rewarded ad');
      _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdDismissedFullScreenContent: (ad) {
          print('✅ AdService: Rewarded ad dismissed');
          ad.dispose();
          _preloadRewardedAd();
        },
        onAdFailedToShowFullScreenContent: (ad, err) {
          print('❌ AdService: Rewarded ad failed to show: ${err.message}');
          ad.dispose();
          _preloadRewardedAd();
        },
      );
      _rewardedAd!.show(
          onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        print('🎉 AdService: User earned reward: ${reward.amount} ${reward.type}');
        onUserEarnedReward();
      });
      _isRewardedAdReady = false;
    } else {
      print('❌ AdService: Rewarded ad not ready or null');
      // Call the reward callback anyway for better UX
      onUserEarnedReward();
    }
  }
}
