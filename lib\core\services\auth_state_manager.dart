import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'auth_service.dart';

/// Authentication state manager using ChangeNotifier for state management
class AuthStateManager extends ChangeNotifier {
  static AuthStateManager? _instance;
  final AuthService _authService = AuthService.instance;

  User? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _error;

  /// Singleton instance
  static AuthStateManager get instance {
    _instance ??= AuthStateManager._internal();
    return _instance!;
  }

  AuthStateManager._internal() {
    // Don't initialize immediately to avoid errors during app startup
    // Initialize will be called explicitly when needed
  }

  /// Getters
  User? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize authentication state from storage
  Future<void> initializeAuthState() async {
    if (_isLoading) return; // Prevent multiple initializations

    _setLoading(true);

    try {
      final isAuth = await _authService.isAuthenticated();
      if (isAuth) {
        final user = await _authService.getCurrentUser();
        if (user != null) {
          _currentUser = user;
          _isAuthenticated = true;
        } else {
          // Clear invalid auth state
          await _authService.clearAuthData();
          _isAuthenticated = false;
        }
      } else {
        _isAuthenticated = false;
      }
    } catch (e) {
      debugPrint('Auth state initialization error: $e');
      // Don't set error during initialization to avoid error screens
      _isAuthenticated = false;
    } finally {
      _setLoading(false);
    }
  }

  /// Login user
  Future<bool> login({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.login(
        email: email,
        password: password,
      );

      if (response.success && response.data != null) {
        _currentUser = response.data!.user;
        _isAuthenticated = true;
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Register user
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.register(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
      );

      if (response.success && response.data != null) {
        _currentUser = response.data!.user;
        _isAuthenticated = true;
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Set guest user mode
  void setGuestMode() {
    _currentUser = User(
      id: -1, // Special ID for guest users
      name: 'Guest User',
      email: '<EMAIL>',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _isAuthenticated = false; // Guest is not authenticated but has a user object
    notifyListeners();
  }

  /// Check if current user is guest
  bool get isGuestMode => _currentUser?.id == -1;

  /// Logout user
  Future<void> logout() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.logout();
    } catch (e) {
      debugPrint('Logout error: $e');
    } finally {
      _currentUser = null;
      _isAuthenticated = false;
      _setLoading(false);
    }
  }

  /// Refresh user profile
  Future<bool> refreshProfile() async {
    if (!_isAuthenticated) return false;

    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.refreshProfile();

      if (response.success && response.data != null) {
        _currentUser = response.data!;
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to refresh profile: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Check authentication status
  Future<void> checkAuthStatus() async {
    _setLoading(true);
    
    try {
      final isAuth = await _authService.isAuthenticated();
      if (isAuth != _isAuthenticated) {
        if (isAuth) {
          final user = await _authService.getCurrentUser();
          if (user != null) {
            _currentUser = user;
            _isAuthenticated = true;
          } else {
            await _authService.clearAuthData();
            _currentUser = null;
            _isAuthenticated = false;
          }
        } else {
          _currentUser = null;
          _isAuthenticated = false;
        }
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to check authentication status');
    } finally {
      _setLoading(false);
    }
  }

  /// Update user data
  void updateUser(User user) {
    _currentUser = user;
    notifyListeners();
  }

  /// Clear error
  void clearError() {
    _clearError();
  }

  /// Force logout (clear all data)
  Future<void> forceLogout() async {
    await _authService.clearAuthData();
    _currentUser = null;
    _isAuthenticated = false;
    _clearError();
    notifyListeners();
  }

  /// Check if token will expire soon
  Future<bool> willTokenExpireSoon() async {
    return await _authService.willTokenExpireSoon();
  }

  /// Get time until token expires
  Future<Duration?> getTimeUntilTokenExpiry() async {
    return await _authService.getTimeUntilTokenExpiry();
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  /// Dispose resources
  @override
  void dispose() {
    super.dispose();
  }
}
