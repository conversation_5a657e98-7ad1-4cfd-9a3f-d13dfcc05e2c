import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/category_model.dart' as CategoryModel;
import 'http_client_service.dart';

/// Service for managing transaction categories
class CategoryService {
  static CategoryService? _instance;
  final HttpClientService _httpClient = HttpClientService.instance;

  /// Singleton instance
  static CategoryService get instance {
    _instance ??= CategoryService._internal();
    return _instance!;
  }

  CategoryService._internal();

  /// Get all categories (system + user categories)
  Future<ApiResponse<List<CategoryModel.Category>>> getCategories({String? type}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (type != null) queryParams['type'] = type;

      final response = await _httpClient.get<List<CategoryModel.Category>>(
        ApiConfig.categoriesEndpoint,
        queryParameters: queryParams,
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => CategoryModel.Category.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Get categories error: $e');
      return ApiResponse.error(
        message: 'Failed to get categories',
        error: e.toString(),
      );
    }
  }

  /// Get expense categories
  Future<ApiResponse<List<CategoryModel.Category>>> getExpenseCategories() async {
    return getCategories(type: 'expense');
  }

  /// Get income categories
  Future<ApiResponse<List<CategoryModel.Category>>> getIncomeCategories() async {
    return getCategories(type: 'income');
  }

  /// Get a specific category by ID
  Future<ApiResponse<CategoryModel.Category>> getCategory(int categoryId) async {
    try {
      final response = await _httpClient.get<CategoryModel.Category>(
        '${ApiConfig.categoriesEndpoint}/$categoryId',
        fromJson: (json) => CategoryModel.Category.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Get category error: $e');
      return ApiResponse.error(
        message: 'Failed to get category',
        error: e.toString(),
      );
    }
  }

  /// Create a new custom category
  Future<ApiResponse<CategoryModel.Category>> createCategory({
    required String name,
    required String type,
    String? parentCategory,
    String? description,
    String? icon,
    String? color,
    int sortOrder = 0,
  }) async {
    try {
      final data = {
        'name': name,
        'type': type,
        'sort_order': sortOrder,
      };

      if (parentCategory != null) data['parent_category'] = parentCategory;
      if (description != null) data['description'] = description;
      if (icon != null) data['icon'] = icon;
      if (color != null) data['color'] = color;

      final response = await _httpClient.post<CategoryModel.Category>(
        ApiConfig.categoriesEndpoint,
        data: data,
        fromJson: (json) => CategoryModel.Category.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Create category error: $e');
      return ApiResponse.error(
        message: 'Failed to create category',
        error: e.toString(),
      );
    }
  }

  /// Update an existing category (only user-created categories)
  Future<ApiResponse<CategoryModel.Category>> updateCategory({
    required int categoryId,
    String? name,
    String? type,
    String? parentCategory,
    String? description,
    String? icon,
    String? color,
    bool? isActive,
    int? sortOrder,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (name != null) data['name'] = name;
      if (type != null) data['type'] = type;
      if (parentCategory != null) data['parent_category'] = parentCategory;
      if (description != null) data['description'] = description;
      if (icon != null) data['icon'] = icon;
      if (color != null) data['color'] = color;
      if (isActive != null) data['is_active'] = isActive ? 1 : 0;
      if (sortOrder != null) data['sort_order'] = sortOrder;

      final response = await _httpClient.put<CategoryModel.Category>(
        '${ApiConfig.categoriesEndpoint}/$categoryId',
        data: data,
        fromJson: (json) => CategoryModel.Category.fromJson(json),
      );

      return response;
    } catch (e) {
      debugPrint('Update category error: $e');
      return ApiResponse.error(
        message: 'Failed to update category',
        error: e.toString(),
      );
    }
  }

  /// Delete a category (only user-created categories)
  Future<ApiResponse<void>> deleteCategory(int categoryId) async {
    try {
      final response = await _httpClient.delete<void>(
        '${ApiConfig.categoriesEndpoint}/$categoryId',
      );

      return response;
    } catch (e) {
      debugPrint('Delete category error: $e');
      return ApiResponse.error(
        message: 'Failed to delete category',
        error: e.toString(),
      );
    }
  }

  /// Get system categories (default categories)
  Future<ApiResponse<List<CategoryModel.Category>>> getSystemCategories({String? type}) async {
    try {
      final queryParams = <String, dynamic>{'system': '1'};
      if (type != null) queryParams['type'] = type;

      final response = await _httpClient.get<List<CategoryModel.Category>>(
        ApiConfig.categoriesEndpoint,
        queryParameters: queryParams,
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => CategoryModel.Category.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Get system categories error: $e');
      return ApiResponse.error(
        message: 'Failed to get system categories',
        error: e.toString(),
      );
    }
  }

  /// Get user custom categories
  Future<ApiResponse<List<CategoryModel.Category>>> getUserCategories({String? type}) async {
    try {
      final queryParams = <String, dynamic>{'user': '1'};
      if (type != null) queryParams['type'] = type;

      final response = await _httpClient.get<List<CategoryModel.Category>>(
        ApiConfig.categoriesEndpoint,
        queryParameters: queryParams,
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => CategoryModel.Category.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Get user categories error: $e');
      return ApiResponse.error(
        message: 'Failed to get user categories',
        error: e.toString(),
      );
    }
  }

  /// Initialize default categories for a new user
  Future<ApiResponse<void>> initializeDefaultCategories() async {
    try {
      final response = await _httpClient.post<void>(
        '${ApiConfig.categoriesEndpoint}/initialize-defaults',
      );

      return response;
    } catch (e) {
      debugPrint('Initialize default categories error: $e');
      return ApiResponse.error(
        message: 'Failed to initialize default categories',
        error: e.toString(),
      );
    }
  }

  /// Get category usage statistics
  Future<ApiResponse<Map<String, dynamic>>> getCategoryStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

      final response = await _httpClient.get<Map<String, dynamic>>(
        '${ApiConfig.categoriesEndpoint}/stats',
        queryParameters: queryParams,
        fromJson: (json) => json as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      debugPrint('Get category stats error: $e');
      return ApiResponse.error(
        message: 'Failed to get category statistics',
        error: e.toString(),
      );
    }
  }

  /// Search categories
  Future<ApiResponse<List<CategoryModel.Category>>> searchCategories({
    required String query,
    String? type,
  }) async {
    try {
      final queryParams = <String, dynamic>{'search': query};
      if (type != null) queryParams['type'] = type;

      final response = await _httpClient.get<List<CategoryModel.Category>>(
        '${ApiConfig.categoriesEndpoint}/search',
        queryParameters: queryParams,
        fromJson: (json) {
          if (json is List) {
            return json.map((item) => CategoryModel.Category.fromJson(item)).toList();
          }
          return [];
        },
      );

      return response;
    } catch (e) {
      debugPrint('Search categories error: $e');
      return ApiResponse.error(
        message: 'Failed to search categories',
        error: e.toString(),
      );
    }
  }
}
