import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math';

import '../../../core/app_export.dart';
import '../../../widgets/ads/native_ad_widget.dart';

class RecentTransactionsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> transactions;
  final VoidCallback onViewAll;
  final Function(Map<String, dynamic>) onEditTransaction;
  final Function(Map<String, dynamic>) onDeleteTransaction;

  const RecentTransactionsWidget({
    Key? key,
    required this.transactions,
    required this.onViewAll,
    required this.onEditTransaction,
    required this.onDeleteTransaction,
  }) : super(key: key);

  String _formatCurrency(double amount) {
    return FormattingService.instance.formatCurrency(amount);
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '${difference} days ago';
    } else {
      return '${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}';
    }
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return const Color(0xFFFF6B6B);
      case 'transport':
        return const Color(0xFF4ECDC4);
      case 'shopping':
        return const Color(0xFFFFE66D);
      case 'entertainment':
        return const Color(0xFF95E1D3);
      case 'bills':
        return const Color(0xFFFFA726);
      case 'salary':
        return AppTheme.successGreen;
      case 'freelance':
        return const Color(0xFF66BB6A);
      case 'investment':
        return const Color(0xFF42A5F5);
      default:
        return AppTheme.neutralGray;
    }
  }

  String _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'food':
        return 'restaurant';
      case 'transport':
        return 'directions_car';
      case 'shopping':
        return 'shopping_bag';
      case 'entertainment':
        return 'movie';
      case 'bills':
        return 'receipt';
      case 'salary':
        return 'account_balance_wallet';
      case 'freelance':
        return 'work';
      case 'investment':
        return 'trending_up';
      default:
        return 'category';
    }
  }

  Widget _buildTransactionItem(
      Map<String, dynamic> transaction, bool isDark, BuildContext context) {
    final isIncome = (transaction['type'] as String).toLowerCase() == 'income';
    final amount = transaction['amount'] as double;
    final category = transaction['category'] as String;
    final description = transaction['description'] as String;
    final date = transaction['date'] as DateTime;
    final account = transaction['account'] as String;

    return Dismissible(
      key: Key(transaction['id'].toString()),
      background: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.only(left: 6.w),
        decoration: BoxDecoration(
          color: AppTheme.primaryTeal.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: CustomIconWidget(
          iconName: 'edit',
          color: AppTheme.primaryTeal,
          size: 24,
        ),
      ),
      secondaryBackground: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 6.w),
        decoration: BoxDecoration(
          color: AppTheme.alertRed.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: CustomIconWidget(
          iconName: 'delete',
          color: AppTheme.alertRed,
          size: 24,
        ),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          onEditTransaction(transaction);
          return false;
        } else {
          return await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Delete Transaction'),
                  content: const Text(
                      'Are you sure you want to delete this transaction?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text('Delete'),
                    ),
                  ],
                ),
              ) ??
              false;
        }
      },
      onDismissed: (direction) {
        if (direction == DismissDirection.endToStart) {
          onDeleteTransaction(transaction);
        }
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 2.h),
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: isDark ? AppTheme.cardSurfaceDark : AppTheme.cardSurface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isDark
                ? AppTheme.neutralGray.withValues(alpha: 0.2)
                : AppTheme.borderSubtle,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: _getCategoryColor(category).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: CustomIconWidget(
                iconName: _getCategoryIcon(category),
                color: _getCategoryColor(category),
                size: 24,
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          description,
                          style: GoogleFonts.inter(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: isDark
                                ? AppTheme.textHighEmphasisDark
                                : AppTheme.textHighEmphasisLight,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        '${isIncome ? '+' : '-'}${_formatCurrency(amount)}',
                        style: AppTheme.financialAmountStyle(
                          isLight: !isDark,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ).copyWith(
                          color: isIncome
                              ? AppTheme.successGreen
                              : AppTheme.alertRed,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 1.h),
                  Row(
                    children: [
                      Text(
                        category,
                        style: GoogleFonts.inter(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: _getCategoryColor(category),
                        ),
                      ),
                      SizedBox(width: 2.w),
                      Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          color: isDark
                              ? AppTheme.textDisabledDark
                              : AppTheme.textDisabledLight,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        account,
                        style: GoogleFonts.inter(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: isDark
                              ? AppTheme.textMediumEmphasisDark
                              : AppTheme.textMediumEmphasisLight,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        _formatDate(date),
                        style: GoogleFonts.inter(
                          fontSize: 11.sp,
                          fontWeight: FontWeight.w400,
                          color: isDark
                              ? AppTheme.textDisabledDark
                              : AppTheme.textDisabledLight,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ListenableBuilder(
      listenable: FormattingService.instance,
      builder: (context, child) {
        return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Transactions',
                style: GoogleFonts.inter(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: isDark
                      ? AppTheme.textHighEmphasisDark
                      : AppTheme.textHighEmphasisLight,
                ),
              ),
              TextButton(
                onPressed: onViewAll,
                child: Text(
                  'View All',
                  style: GoogleFonts.inter(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.primaryTeal,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          transactions.isEmpty
              ? Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: isDark
                        ? AppTheme.cardSurfaceDark
                        : AppTheme.cardSurface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isDark
                          ? AppTheme.neutralGray.withValues(alpha: 0.2)
                          : AppTheme.borderSubtle,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      CustomIconWidget(
                        iconName: 'receipt_long',
                        color: isDark
                            ? AppTheme.textDisabledDark
                            : AppTheme.textDisabledLight,
                        size: 48,
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        'No transactions yet',
                        style: GoogleFonts.inter(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                          color: isDark
                              ? AppTheme.textMediumEmphasisDark
                              : AppTheme.textMediumEmphasisLight,
                        ),
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        'Add your first transaction to get started',
                        style: GoogleFonts.inter(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                          color: isDark
                              ? AppTheme.textDisabledDark
                              : AppTheme.textDisabledLight,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : _buildTransactionListWithAds(transactions.take(5).toList(), isDark, context),
        ],
      ),
        );
      },
    );
  }

  /// Build transaction list with randomly placed native ads
  Widget _buildTransactionListWithAds(List<Map<String, dynamic>> transactions, bool isDark, BuildContext context) {
    if (transactions.isEmpty) {
      return const SizedBox.shrink();
    }

    // Generate random ad positions (every 3-4 transactions)
    final List<Widget> items = [];
    final Random random = Random();
    int nextAdPosition = 3 + random.nextInt(2); // 3 or 4

    for (int i = 0; i < transactions.length; i++) {
      // Add transaction item
      items.add(_buildTransactionItem(transactions[i], isDark, context));

      // Add native ad if we've reached the ad position and it's not the last item
      if (i + 1 == nextAdPosition && i < transactions.length - 1) {
        items.add(
          Padding(
            padding: EdgeInsets.symmetric(vertical: 1.h),
            child: const NativeAdWidget(),
          ),
        );
        // Set next ad position (3-4 transactions later)
        nextAdPosition = i + 1 + 3 + random.nextInt(2);
      }
    }

    return Column(children: items);
  }
}
