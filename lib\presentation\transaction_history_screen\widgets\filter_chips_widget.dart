import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class FilterChipsWidget extends StatelessWidget {
  final Map<String, dynamic> activeFilters;
  final Function(String, dynamic) onRemoveFilter;

  const FilterChipsWidget({
    super.key,
    required this.activeFilters,
    required this.onRemoveFilter,
  });

  @override
  Widget build(BuildContext context) {
    final chips = _buildFilterChips();

    if (chips.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Active Filters',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
              SizedBox(width: 2.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                decoration: BoxDecoration(
                  color: AppTheme.primaryTeal,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  chips.length.toString(),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          Wrap(
            spacing: 2.w,
            runSpacing: 1.h,
            children: chips,
          ),
          SizedBox(height: 2.h),
        ],
      ),
    );
  }

  List<Widget> _buildFilterChips() {
    final List<Widget> chips = [];

    // Date range chip
    if (activeFilters['startDate'] != null ||
        activeFilters['endDate'] != null) {
      String dateText = '';
      if (activeFilters['startDate'] != null &&
          activeFilters['endDate'] != null) {
        dateText =
            '${_formatDate(activeFilters['startDate'] as DateTime)} - ${_formatDate(activeFilters['endDate'] as DateTime)}';
      } else if (activeFilters['startDate'] != null) {
        dateText =
            'From ${_formatDate(activeFilters['startDate'] as DateTime)}';
      } else {
        dateText = 'Until ${_formatDate(activeFilters['endDate'] as DateTime)}';
      }

      chips.add(_buildChip(
        label: dateText,
        onRemove: () {
          onRemoveFilter('startDate', null);
          onRemoveFilter('endDate', null);
        },
      ));
    }

    // Categories chips
    final categories = activeFilters['categories'] as List<String>?;
    if (categories != null && categories.isNotEmpty) {
      if (categories.length == 1) {
        chips.add(_buildChip(
          label: categories.first,
          onRemove: () => onRemoveFilter('categories', null),
        ));
      } else {
        chips.add(_buildChip(
          label: '${categories.length} Categories',
          onRemove: () => onRemoveFilter('categories', null),
        ));
      }
    }

    // Account types chips
    final accountTypes = activeFilters['accountTypes'] as List<String>?;
    if (accountTypes != null && accountTypes.isNotEmpty) {
      if (accountTypes.length == 1) {
        chips.add(_buildChip(
          label: accountTypes.first,
          onRemove: () => onRemoveFilter('accountTypes', null),
        ));
      } else {
        chips.add(_buildChip(
          label: '${accountTypes.length} Accounts',
          onRemove: () => onRemoveFilter('accountTypes', null),
        ));
      }
    }

    // Amount range chip
    if (activeFilters['minAmount'] != null ||
        activeFilters['maxAmount'] != null) {
      final minAmount = (activeFilters['minAmount'] as double?) ?? 0.0;
      final maxAmount = (activeFilters['maxAmount'] as double?) ?? 100000.0;

      if (minAmount > 0 || maxAmount < 100000) {
        chips.add(_buildChip(
          label: '₹${minAmount.round()} - ₹${maxAmount.round()}',
          onRemove: () {
            onRemoveFilter('minAmount', null);
            onRemoveFilter('maxAmount', null);
          },
        ));
      }
    }

    // Transaction type chip
    if (activeFilters['transactionType'] != null) {
      chips.add(_buildChip(
        label: activeFilters['transactionType'] as String,
        onRemove: () => onRemoveFilter('transactionType', null),
      ));
    }

    return chips;
  }

  Widget _buildChip({
    required String label,
    required VoidCallback onRemove,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: AppTheme.primaryTeal.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.primaryTeal.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                color: AppTheme.primaryTeal,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 2.w),
          GestureDetector(
            onTap: onRemove,
            child: Container(
              padding: EdgeInsets.all(0.5.w),
              decoration: BoxDecoration(
                color: AppTheme.primaryTeal,
                borderRadius: BorderRadius.circular(10),
              ),
              child: CustomIconWidget(
                iconName: 'close',
                color: Colors.white,
                size: 3.w,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}';
  }
}
