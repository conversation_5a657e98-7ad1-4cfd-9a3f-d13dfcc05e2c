import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

/// Skeleton loading widget with shimmer effect
class SkeletonLoading extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final EdgeInsets? margin;

  const SkeletonLoading({
    Key? key,
    this.width,
    this.height,
    this.borderRadius,
    this.margin,
  }) : super(key: key);

  @override
  State<SkeletonLoading> createState() => _SkeletonLoadingState();
}

class _SkeletonLoadingState extends State<SkeletonLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final baseColor = isDark ? Colors.grey[800]! : Colors.grey[300]!;
    final highlightColor = isDark ? Colors.grey[700]! : Colors.grey[100]!;

    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
        color: baseColor,
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  baseColor,
                  highlightColor,
                  baseColor,
                ],
                stops: [
                  (_animation.value - 1).clamp(0.0, 1.0),
                  _animation.value.clamp(0.0, 1.0),
                  (_animation.value + 1).clamp(0.0, 1.0),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Skeleton loading for balance card
class BalanceCardSkeleton extends StatelessWidget {
  const BalanceCardSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[800]
            : Colors.grey[300],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SkeletonLoading(
                width: 25.w,
                height: 2.h,
                borderRadius: BorderRadius.circular(4),
              ),
              SkeletonLoading(
                width: 8.w,
                height: 8.w,
                borderRadius: BorderRadius.circular(8),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          SkeletonLoading(
            width: 60.w,
            height: 4.h,
            borderRadius: BorderRadius.circular(6),
          ),
          SizedBox(height: 1.h),
          SkeletonLoading(
            width: 30.w,
            height: 1.5.h,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      ),
    );
  }
}

/// Skeleton loading for income/expense cards
class IncomeExpenseCardsSkeleton extends StatelessWidget {
  const IncomeExpenseCardsSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[800]
                    : Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      SkeletonLoading(
                        width: 6.w,
                        height: 6.w,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      SizedBox(width: 2.w),
                      SkeletonLoading(
                        width: 15.w,
                        height: 2.h,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                  SizedBox(height: 2.h),
                  SkeletonLoading(
                    width: 25.w,
                    height: 3.h,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  SizedBox(height: 1.h),
                  SkeletonLoading(
                    width: 20.w,
                    height: 1.5.h,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[800]
                    : Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      SkeletonLoading(
                        width: 6.w,
                        height: 6.w,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      SizedBox(width: 2.w),
                      SkeletonLoading(
                        width: 15.w,
                        height: 2.h,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                  SizedBox(height: 2.h),
                  SkeletonLoading(
                    width: 25.w,
                    height: 3.h,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  SizedBox(height: 1.h),
                  SkeletonLoading(
                    width: 20.w,
                    height: 1.5.h,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Skeleton loading for recent transactions
class RecentTransactionsSkeleton extends StatelessWidget {
  const RecentTransactionsSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SkeletonLoading(
                width: 40.w,
                height: 3.h,
                borderRadius: BorderRadius.circular(4),
              ),
              SkeletonLoading(
                width: 15.w,
                height: 2.h,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          ...List.generate(
            3,
            (index) => Container(
              margin: EdgeInsets.only(bottom: 2.h),
              child: Row(
                children: [
                  SkeletonLoading(
                    width: 12.w,
                    height: 12.w,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SkeletonLoading(
                          width: 30.w,
                          height: 2.h,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        SizedBox(height: 1.h),
                        SkeletonLoading(
                          width: 50.w,
                          height: 1.5.h,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ],
                    ),
                  ),
                  SkeletonLoading(
                    width: 20.w,
                    height: 2.5.h,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
