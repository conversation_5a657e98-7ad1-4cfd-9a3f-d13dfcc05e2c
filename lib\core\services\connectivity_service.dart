import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import '../config/api_config.dart';

/// Connectivity service for monitoring network status and managing online/offline states
class ConnectivityService extends ChangeNotifier {
  static ConnectivityService? _instance;
  
  /// Singleton instance
  static ConnectivityService get instance {
    _instance ??= ConnectivityService._internal();
    return _instance!;
  }

  ConnectivityService._internal() {
    _initialize();
  }

  // Private fields
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  bool _isOnline = true;
  bool _hasInternetAccess = true;
  DateTime? _lastConnectivityCheck;
  Timer? _internetCheckTimer;

  // Getters
  bool get isOnline => _isOnline && _hasInternetAccess;
  bool get hasConnectivity => _isOnline;
  bool get hasInternetAccess => _hasInternetAccess;
  DateTime? get lastConnectivityCheck => _lastConnectivityCheck;

  /// Initialize connectivity monitoring
  void _initialize() {
    debugPrint('📡 Initializing connectivity service...');
    
    // Check initial connectivity
    _checkInitialConnectivity();
    
    // Listen to connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _onConnectivityChanged,
      onError: (error) {
        debugPrint('❌ Connectivity stream error: $error');
      },
    );

    // Start periodic internet access checks
    _startInternetAccessChecks();
  }

  /// Check initial connectivity status
  Future<void> _checkInitialConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      await _onConnectivityChanged(connectivityResults);
    } catch (e) {
      debugPrint('❌ Initial connectivity check error: $e');
      _updateConnectivityStatus(false, false);
    }
  }

  /// Handle connectivity changes
  Future<void> _onConnectivityChanged(List<ConnectivityResult> results) async {
    debugPrint('📡 Connectivity changed: $results');
    
    final hasConnection = results.any((result) => 
      result == ConnectivityResult.mobile ||
      result == ConnectivityResult.wifi ||
      result == ConnectivityResult.ethernet
    );

    if (hasConnection) {
      // Check if we actually have internet access
      final hasInternet = await _checkInternetAccess();
      _updateConnectivityStatus(true, hasInternet);
    } else {
      _updateConnectivityStatus(false, false);
    }
  }

  /// Check if we have actual internet access by pinging a reliable server
  Future<bool> _checkInternetAccess() async {
    try {
      // Try to reach Google's DNS first (fastest)
      final result = await InternetAddress.lookup('google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        // Also try to reach our API server
        return await _checkApiServerAccess();
      }
      return false;
    } catch (e) {
      debugPrint('📡 Internet access check failed: $e');
      return false;
    }
  }

  /// Check if our API server is accessible
  Future<bool> _checkApiServerAccess() async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 5);
      dio.options.receiveTimeout = const Duration(seconds: 5);
      
      final response = await dio.get(ApiConfig.getFullUrl('/test'));
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('📡 API server access check failed: $e');
      // Even if API is down, we might have internet for other services
      return true;
    }
  }

  /// Update connectivity status and notify listeners
  void _updateConnectivityStatus(bool hasConnectivity, bool hasInternetAccess) {
    final wasOnline = isOnline;
    
    _isOnline = hasConnectivity;
    _hasInternetAccess = hasInternetAccess;
    _lastConnectivityCheck = DateTime.now();

    final isNowOnline = isOnline;
    
    debugPrint('📡 Connectivity status updated: '
        'hasConnectivity=$hasConnectivity, '
        'hasInternetAccess=$hasInternetAccess, '
        'isOnline=$isNowOnline');

    // Notify listeners if status changed
    if (wasOnline != isNowOnline) {
      debugPrint('📡 Network status changed: ${wasOnline ? 'ONLINE' : 'OFFLINE'} → ${isNowOnline ? 'ONLINE' : 'OFFLINE'}');
      notifyListeners();
    }
  }

  /// Start periodic internet access checks
  void _startInternetAccessChecks() {
    _internetCheckTimer?.cancel();
    _internetCheckTimer = Timer.periodic(
      const Duration(seconds: 30),
      (timer) async {
        if (_isOnline) {
          final hasInternet = await _checkInternetAccess();
          if (hasInternet != _hasInternetAccess) {
            _updateConnectivityStatus(_isOnline, hasInternet);
          }
        }
      },
    );
  }

  /// Force refresh connectivity status
  Future<void> refreshConnectivityStatus() async {
    debugPrint('📡 Force refreshing connectivity status...');
    await _checkInitialConnectivity();
  }

  /// Get connectivity type as string
  String getConnectivityType() {
    if (!_isOnline) return 'No Connection';
    if (!_hasInternetAccess) return 'Limited Connection';
    return 'Connected';
  }

  /// Get detailed connectivity info
  Map<String, dynamic> getConnectivityInfo() {
    return {
      'isOnline': isOnline,
      'hasConnectivity': hasConnectivity,
      'hasInternetAccess': hasInternetAccess,
      'connectivityType': getConnectivityType(),
      'lastCheck': _lastConnectivityCheck?.toIso8601String(),
    };
  }

  /// Wait for internet connection
  Future<bool> waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    if (isOnline) return true;

    final completer = Completer<bool>();
    late StreamSubscription subscription;
    Timer? timeoutTimer;

    void cleanup() {
      subscription.cancel();
      timeoutTimer?.cancel();
    }

    subscription = _connectivity.onConnectivityChanged.listen((results) async {
      final hasConnection = results.any((result) => 
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet
      );

      if (hasConnection) {
        final hasInternet = await _checkInternetAccess();
        if (hasInternet) {
          cleanup();
          if (!completer.isCompleted) {
            completer.complete(true);
          }
        }
      }
    });

    timeoutTimer = Timer(timeout, () {
      cleanup();
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    return completer.future;
  }

  /// Dispose resources
  @override
  void dispose() {
    debugPrint('📡 Disposing connectivity service...');
    _connectivitySubscription?.cancel();
    _internetCheckTimer?.cancel();
    super.dispose();
  }
}
