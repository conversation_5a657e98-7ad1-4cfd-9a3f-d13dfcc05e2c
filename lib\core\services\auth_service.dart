import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/user_model.dart';
import 'http_client_service.dart';
import 'token_storage_service.dart';

/// Authentication service for handling user authentication
class AuthService {
  static AuthService? _instance;
  final HttpClientService _httpClient = HttpClientService.instance;
  final TokenStorageService _tokenStorage = TokenStorageService.instance;

  /// Singleton instance
  static AuthService get instance {
    _instance ??= AuthService._internal();
    return _instance!;
  }

  AuthService._internal();

  /// Test API connection
  Future<ApiResponse<TestResponse>> testConnection() async {
    try {
      final response = await _httpClient.get<TestResponse>(
        ApiConfig.testEndpoint,
        fromJson: (json) => TestResponse.fromJson(json),
      );
      return response;
    } catch (e) {
      debugPrint('Test connection error: $e');
      return ApiResponse.error(
        message: 'Failed to connect to server',
        error: e.toString(),
      );
    }
  }

  /// Register a new user
  Future<ApiResponse<AuthResponse>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    try {
      final response = await _httpClient.post<AuthResponse>(
        ApiConfig.registerEndpoint,
        data: {
          'name': name.trim(),
          'email': email.trim().toLowerCase(),
          'password': password,
          'password_confirmation': passwordConfirmation,
        },
        fromJson: (json) => AuthResponse.fromJson(json),
      );

      // Save authentication data if successful
      if (response.success && response.data != null) {
        await _tokenStorage.saveAuthData(
          response.data!.token,
          response.data!.user,
        );
      }

      return response;
    } catch (e) {
      debugPrint('Registration error: $e');
      return ApiResponse.error(
        message: 'Registration failed',
        error: e.toString(),
      );
    }
  }

  /// Login user
  Future<ApiResponse<AuthResponse>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _httpClient.post<AuthResponse>(
        ApiConfig.loginEndpoint,
        data: {
          'email': email.trim().toLowerCase(),
          'password': password,
        },
        fromJson: (json) => AuthResponse.fromJson(json),
      );

      // Save authentication data if successful
      if (response.success && response.data != null) {
        await _tokenStorage.saveAuthData(
          response.data!.token,
          response.data!.user,
        );
      }

      return response;
    } catch (e) {
      debugPrint('Login error: $e');
      return ApiResponse.error(
        message: 'Login failed',
        error: e.toString(),
      );
    }
  }

  /// Logout user
  Future<ApiResponse<void>> logout() async {
    try {
      final response = await _httpClient.post<void>(
        ApiConfig.logoutEndpoint,
      );

      // Clear local authentication data regardless of API response
      await _tokenStorage.clearAllAuthData();

      return response;
    } catch (e) {
      debugPrint('Logout error: $e');
      // Still clear local data even if API call fails
      await _tokenStorage.clearAllAuthData();
      
      return ApiResponse.error(
        message: 'Logout failed',
        error: e.toString(),
      );
    }
  }

  /// Send forgot password email
  Future<ApiResponse<void>> forgotPassword({
    required String email,
  }) async {
    try {
      final response = await _httpClient.post<void>(
        ApiConfig.forgotPasswordEndpoint,
        data: {
          'email': email.trim().toLowerCase(),
        },
      );

      return response;
    } catch (e) {
      debugPrint('Forgot password error: $e');
      return ApiResponse.error(
        message: 'Failed to send reset email',
        error: e.toString(),
      );
    }
  }

  /// Reset password with token
  Future<ApiResponse<void>> resetPassword({
    required String token,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    try {
      final response = await _httpClient.post<void>(
        ApiConfig.resetPasswordEndpoint,
        data: {
          'token': token,
          'email': email.trim().toLowerCase(),
          'password': password,
          'password_confirmation': passwordConfirmation,
        },
      );

      return response;
    } catch (e) {
      debugPrint('Reset password error: $e');
      return ApiResponse.error(
        message: 'Failed to reset password',
        error: e.toString(),
      );
    }
  }

  /// Get user profile
  Future<ApiResponse<User>> getProfile() async {
    try {
      final response = await _httpClient.get<User>(
        ApiConfig.profileEndpoint,
        fromJson: (json) => User.fromJson(json['user'] ?? json),
      );

      // Update local user data if successful
      if (response.success && response.data != null) {
        await _tokenStorage.saveUserData(response.data!);
      }

      return response;
    } catch (e) {
      debugPrint('Get profile error: $e');
      return ApiResponse.error(
        message: 'Failed to get profile',
        error: e.toString(),
      );
    }
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    return await _tokenStorage.isLoggedIn();
  }

  /// Get current user from local storage
  Future<User?> getCurrentUser() async {
    return await _tokenStorage.getUserData();
  }

  /// Get current auth token
  Future<String?> getCurrentToken() async {
    return await _tokenStorage.getToken();
  }

  /// Check if token is valid
  Future<bool> hasValidToken() async {
    return await _tokenStorage.hasValidToken();
  }

  /// Refresh user profile from server
  Future<ApiResponse<User>> refreshProfile() async {
    return await getProfile();
  }

  /// Clear all authentication data (force logout)
  Future<void> clearAuthData() async {
    await _tokenStorage.clearAllAuthData();
  }

  /// Check if token will expire soon
  Future<bool> willTokenExpireSoon() async {
    return await _tokenStorage.willExpireSoon();
  }

  /// Get time until token expires
  Future<Duration?> getTimeUntilTokenExpiry() async {
    return await _tokenStorage.getTimeUntilExpiry();
  }

  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Validate password strength
  bool isValidPassword(String password) {
    // At least 6 characters
    return password.length >= 6;
  }

  /// Validate name
  bool isValidName(String name) {
    return name.trim().isNotEmpty && name.trim().length >= 2;
  }

  /// Get password strength score (0-4)
  int getPasswordStrength(String password) {
    int score = 0;
    
    if (password.length >= 8) score++;
    if (password.contains(RegExp(r'[A-Z]'))) score++;
    if (password.contains(RegExp(r'[a-z]'))) score++;
    if (password.contains(RegExp(r'[0-9]'))) score++;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++;
    
    return score > 4 ? 4 : score;
  }

  /// Get password strength text
  String getPasswordStrengthText(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'Weak';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Strong';
      default:
        return 'Weak';
    }
  }

  /// Change user password
  Future<ApiResponse<Map<String, dynamic>>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // Check if user is authenticated
      final token = await _tokenStorage.getToken();
      if (token == null) {
        return ApiResponse.error(
          message: 'Authentication required. Please log in again.',
        );
      }

      final response = await _httpClient.post<Map<String, dynamic>>(
        ApiConfig.changePasswordEndpoint,
        data: {
          'current_password': currentPassword,
          'new_password': newPassword,
        },
        fromJson: (json) => json as Map<String, dynamic>,
      );

      // If successful, update the stored token with the new one
      if (response.success && response.data != null) {
        final newToken = response.data!['data']?['token'];
        if (newToken != null) {
          await _tokenStorage.saveToken(newToken);
          debugPrint('✅ Token updated after password change');
        }
      }

      return response;
    } catch (e) {
      debugPrint('Change password error: $e');
      return ApiResponse.error(
        message: 'Failed to change password',
        error: e.toString(),
      );
    }
  }
}
